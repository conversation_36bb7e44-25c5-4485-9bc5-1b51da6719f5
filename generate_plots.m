function generate_plots()
% GENERATE_PLOTS - Generate high-quality PNG plots for joint mutual information analysis
% This function loads the simulation results and creates publication-quality plots

%% Load simulation results
if ~exist('joint_mutual_info_results.mat', 'file')
    error('Simulation results not found. Please run the simulation first.');
end

fprintf('Loading simulation results...\n');
load('joint_mutual_info_results.mat', 'results');

%% Set up plotting parameters
set(0, 'DefaultAxesFontSize', 12);
set(0, 'DefaultTextFontSize', 12);
set(0, 'DefaultLineLineWidth', 2);
set(0, 'DefaultAxesLineWidth', 1.5);

% Color scheme
colors = [
    0.0000, 0.4470, 0.7410;  % Blue
    0.8500, 0.3250, 0.0980;  % Red
    0.9290, 0.6940, 0.1250;  % Yellow
    0.4940, 0.1840, 0.5560;  % Purple
    0.4660, 0.6740, 0.1880;  % Green
    0.3010, 0.7450, 0.9330;  % Light Blue
    0.6350, 0.0780, 0.1840;  % Dark Red
];

SNR_dB_range = results.params.SNR_dB_range;

%% Plot 1: Joint Mutual Information Components
fprintf('Generating Plot 1: Mutual Information Components...\n');
figure('Position', [100, 100, 800, 600]);
hold on;

plot(SNR_dB_range, real(results.joint_mutual_info), '-o', ...
     'Color', colors(1,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(1,:));
plot(SNR_dB_range, real(results.sensing_mutual_info), '--s', ...
     'Color', colors(2,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(2,:));
plot(SNR_dB_range, real(results.comm_mutual_info), '-.^', ...
     'Color', colors(3,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(3,:));
plot(SNR_dB_range, real(results.coupling_info), ':', ...
     'Color', colors(4,:), 'LineWidth', 3);

xlabel('SNR (dB)', 'FontSize', 14, 'FontWeight', 'bold');
ylabel('Mutual Information (bits)', 'FontSize', 14, 'FontWeight', 'bold');
title('Joint Mutual Information Analysis for FD-ISAC Systems', ...
      'FontSize', 16, 'FontWeight', 'bold');
legend('Joint MI', 'Sensing MI', 'Communication MI', 'Coupling', ...
       'Location', 'best', 'FontSize', 12);
grid on;
grid minor;
xlim([min(SNR_dB_range), max(SNR_dB_range)]);

% Save as high-quality PNG
print('joint_mutual_info_components.png', '-dpng', '-r300');
fprintf('Saved: joint_mutual_info_components.png\n');

%% Plot 2: Security Metrics
fprintf('Generating Plot 2: Security Metrics...\n');
figure('Position', [200, 200, 800, 600]);

subplot(2, 1, 1);
plot(SNR_dB_range, real(results.FD_JILI), '-o', ...
     'Color', colors(1,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(1,:));
xlabel('SNR (dB)', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('FD-JILI', 'FontSize', 12, 'FontWeight', 'bold');
title('FD-ISAC Joint Information Leakage Index', 'FontSize', 14, 'FontWeight', 'bold');
grid on;
grid minor;

subplot(2, 1, 2);
plot(SNR_dB_range, real(results.FD_WILM), '-s', ...
     'Color', colors(2,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(2,:));
xlabel('SNR (dB)', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('FD-WILM', 'FontSize', 12, 'FontWeight', 'bold');
title('FD-ISAC Weighted Information Leakage Metric', 'FontSize', 14, 'FontWeight', 'bold');
grid on;
grid minor;

% Save as high-quality PNG
print('security_metrics.png', '-dpng', '-r300');
fprintf('Saved: security_metrics.png\n');

%% Plot 3: Information Leakage Ratios
fprintf('Generating Plot 3: Information Leakage Ratios...\n');
figure('Position', [300, 300, 800, 600]);

% Compute prior entropies
H_theta_L = log(2*pi);
H_X = results.params.L * log(det(pi * exp(1) * results.params.Q_x));

sensing_ratio = real(results.sensing_mutual_info) / H_theta_L;
comm_ratio = real(results.comm_mutual_info) / H_X;

hold on;
plot(SNR_dB_range, sensing_ratio, '--s', ...
     'Color', colors(2,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(2,:));
plot(SNR_dB_range, comm_ratio, '-.^', ...
     'Color', colors(3,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(3,:));

xlabel('SNR (dB)', 'FontSize', 14, 'FontWeight', 'bold');
ylabel('Information Leakage Ratio', 'FontSize', 14, 'FontWeight', 'bold');
title('Normalized Information Leakage Analysis', 'FontSize', 16, 'FontWeight', 'bold');
legend('Sensing Leakage Ratio', 'Communication Leakage Ratio', ...
       'Location', 'best', 'FontSize', 12);
grid on;
grid minor;
xlim([min(SNR_dB_range), max(SNR_dB_range)]);

% Save as high-quality PNG
print('information_leakage_ratios.png', '-dpng', '-r300');
fprintf('Saved: information_leakage_ratios.png\n');

%% Plot 4: Coupling Analysis
fprintf('Generating Plot 4: Coupling Analysis...\n');
figure('Position', [400, 400, 800, 600]);

coupling_ratio = real(results.coupling_info) ./ (real(results.joint_mutual_info) + eps);

plot(SNR_dB_range, coupling_ratio, '-o', ...
     'Color', colors(4,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(4,:));

xlabel('SNR (dB)', 'FontSize', 14, 'FontWeight', 'bold');
ylabel('Coupling Ratio', 'FontSize', 14, 'FontWeight', 'bold');
title('Information Coupling Strength Analysis', 'FontSize', 16, 'FontWeight', 'bold');
grid on;
grid minor;
xlim([min(SNR_dB_range), max(SNR_dB_range)]);

% Add horizontal line at zero
yline(0, '--k', 'LineWidth', 1);

% Save as high-quality PNG
print('coupling_analysis.png', '-dpng', '-r300');
fprintf('Saved: coupling_analysis.png\n');

%% Plot 5: Comprehensive Security Analysis
fprintf('Generating Plot 5: Comprehensive Security Analysis...\n');
figure('Position', [500, 500, 1200, 800]);

% Subplot 1: MI Components with area plot
subplot(2, 2, 1);
sensing_pos = max(0, real(results.sensing_mutual_info));
comm_pos = max(0, real(results.comm_mutual_info));
area(SNR_dB_range, [sensing_pos(:), comm_pos(:)], 'LineWidth', 1.5);
colormap([colors(2,:); colors(3,:)]);
xlabel('SNR (dB)', 'FontSize', 12);
ylabel('Mutual Information (bits)', 'FontSize', 12);
title('Information Decomposition', 'FontSize', 14, 'FontWeight', 'bold');
legend('Sensing', 'Communication', 'Location', 'best');
grid on;

% Subplot 2: Security metrics comparison
subplot(2, 2, 2);
yyaxis left;
plot(SNR_dB_range, real(results.FD_JILI), '-o', 'Color', colors(1,:), 'MarkerSize', 6);
ylabel('FD-JILI', 'Color', colors(1,:), 'FontSize', 12);
yyaxis right;
plot(SNR_dB_range, real(results.FD_WILM), '-s', 'Color', colors(2,:), 'MarkerSize', 6);
ylabel('FD-WILM', 'Color', colors(2,:), 'FontSize', 12);
xlabel('SNR (dB)', 'FontSize', 12);
title('Security Metrics', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

% Subplot 3: Joint MI with trend
subplot(2, 2, 3);
plot(SNR_dB_range, real(results.joint_mutual_info), '-o', ...
     'Color', colors(1,:), 'MarkerSize', 6, 'MarkerFaceColor', colors(1,:));
hold on;
% Add polynomial fit
valid_idx = ~isnan(real(results.joint_mutual_info)) & ~isinf(real(results.joint_mutual_info));
if sum(valid_idx) > 3
    p = polyfit(SNR_dB_range(valid_idx), real(results.joint_mutual_info(valid_idx)), 2);
    fit_curve = polyval(p, SNR_dB_range);
    plot(SNR_dB_range, fit_curve, '--', 'Color', colors(2,:), 'LineWidth', 2);
    legend('Simulation', 'Polynomial Fit', 'Location', 'best');
end
xlabel('SNR (dB)', 'FontSize', 12);
ylabel('Joint MI (bits)', 'FontSize', 12);
title('Joint Mutual Information', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

% Subplot 4: System performance summary
subplot(2, 2, 4);
% Create a summary metric
security_score = 1 - abs(real(results.FD_JILI));  % Higher is better
performance_score = (real(results.sensing_mutual_info) + real(results.comm_mutual_info)) / 2;
performance_score = performance_score / max(performance_score);  % Normalize

plot(SNR_dB_range, security_score, '-o', 'Color', colors(1,:), 'MarkerSize', 6);
hold on;
plot(SNR_dB_range, performance_score, '-s', 'Color', colors(3,:), 'MarkerSize', 6);
xlabel('SNR (dB)', 'FontSize', 12);
ylabel('Normalized Score', 'FontSize', 12);
title('Security vs Performance Trade-off', 'FontSize', 14, 'FontWeight', 'bold');
legend('Security Score', 'Performance Score', 'Location', 'best');
grid on;

sgtitle('Comprehensive FD-ISAC Security Analysis', 'FontSize', 18, 'FontWeight', 'bold');

% Save as high-quality PNG
print('comprehensive_security_analysis.png', '-dpng', '-r300');
fprintf('Saved: comprehensive_security_analysis.png\n');

%% Plot 6: 3D Surface Plot (if applicable)
fprintf('Generating Plot 6: 3D Visualization...\n');
figure('Position', [600, 600, 800, 600]);

% Create meshgrid for 3D plot
[SNR_mesh, Metric_mesh] = meshgrid(SNR_dB_range, 1:3);
Z_data = [real(results.joint_mutual_info)'; 
          real(results.sensing_mutual_info)'; 
          real(results.comm_mutual_info)'];

surf(SNR_mesh, Metric_mesh, Z_data, 'EdgeColor', 'none', 'FaceAlpha', 0.8);
colormap(jet);
colorbar;

xlabel('SNR (dB)', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('Information Type', 'FontSize', 12, 'FontWeight', 'bold');
zlabel('Mutual Information (bits)', 'FontSize', 12, 'FontWeight', 'bold');
title('3D Mutual Information Landscape', 'FontSize', 14, 'FontWeight', 'bold');

% Set y-axis labels
yticks([1, 2, 3]);
yticklabels({'Joint', 'Sensing', 'Communication'});

view(45, 30);
grid on;

% Save as high-quality PNG
print('3d_mutual_info_landscape.png', '-dpng', '-r300');
fprintf('Saved: 3d_mutual_info_landscape.png\n');

%% Generate summary statistics plot
fprintf('Generating Plot 7: Summary Statistics...\n');
figure('Position', [700, 700, 1000, 600]);

% Create bar plots instead of box plots for better visualization
subplot(1, 2, 1);
mi_data = [mean(real(results.joint_mutual_info)), ...
           mean(real(results.sensing_mutual_info)), ...
           mean(real(results.comm_mutual_info))];
mi_std = [std(real(results.joint_mutual_info)), ...
          std(real(results.sensing_mutual_info)), ...
          std(real(results.comm_mutual_info))];

bar(mi_data, 'FaceColor', colors(1,:), 'EdgeColor', 'k', 'LineWidth', 1);
hold on;
errorbar(1:3, mi_data, mi_std, 'k', 'LineStyle', 'none', 'LineWidth', 2);
set(gca, 'XTickLabel', {'Joint MI', 'Sensing MI', 'Comm MI'});
ylabel('Mean Mutual Information (bits)', 'FontSize', 12);
title('Average MI Across SNR Range', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

subplot(1, 2, 2);
security_data = [mean(real(results.FD_JILI)), mean(real(results.FD_WILM))];
security_std = [std(real(results.FD_JILI)), std(real(results.FD_WILM))];

bar(security_data, 'FaceColor', colors(2,:), 'EdgeColor', 'k', 'LineWidth', 1);
hold on;
errorbar(1:2, security_data, security_std, 'k', 'LineStyle', 'none', 'LineWidth', 2);
set(gca, 'XTickLabel', {'FD-JILI', 'FD-WILM'});
ylabel('Mean Security Metric Value', 'FontSize', 12);
title('Average Security Metrics', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

sgtitle('Statistical Summary of FD-ISAC Security Analysis', 'FontSize', 16, 'FontWeight', 'bold');

% Save as high-quality PNG
print('summary_statistics.png', '-dpng', '-r300');
fprintf('Saved: summary_statistics.png\n');

%% Close all figures to free memory
close all;

%% Print summary
fprintf('\n=== Plot Generation Complete ===\n');
fprintf('Generated PNG files:\n');
fprintf('1. joint_mutual_info_components.png\n');
fprintf('2. security_metrics.png\n');
fprintf('3. information_leakage_ratios.png\n');
fprintf('4. coupling_analysis.png\n');
fprintf('5. comprehensive_security_analysis.png\n');
fprintf('6. 3d_mutual_info_landscape.png\n');
fprintf('7. summary_statistics.png\n');
fprintf('\nAll plots saved with 300 DPI resolution for publication quality.\n');

end
