function open_all_figs()
% OPEN_ALL_FIGS - Open all FIG files in the current directory

fig_files = dir('*.fig');
if isempty(fig_files)
    fprintf('No FIG files found in current directory.\n');
    return;
end

fprintf('Opening %d FIG files...\n', length(fig_files));
for i = 1:length(fig_files)
    fprintf('Opening: %s\n', fig_files(i).name);
    openfig(fig_files(i).name, 'new');
end

fprintf('All FIG files opened successfully.\n');
end
