\documentclass[journal]{IEEEtran}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}

% Math commands
\newcommand{\bm}[1]{\boldsymbol{#1}}
\newcommand{\tr}{\text{tr}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}

\title{Feasibility Analysis and Enhanced Derivation of GUIDE SOP Metric for FD-ISAC Systems}
\author{Based on GUIDE\_SOP\_metric.pdf and Our Signal Model}

\begin{document}
\maketitle

\begin{abstract}
This document analyzes the feasibility of the GUIDE SOP metric proposal and provides an enhanced derivation based on our established FD-ISAC signal model. We evaluate the theoretical soundness, computational complexity, and practical applicability of combining sensing secrecy outage probability (SOP) with communication outage secrecy rate probability (OSP) for comprehensive security assessment in full-duplex ISAC systems.
\end{abstract}

\section{Feasibility Assessment of GUIDE SOP Approach}

\subsection{Theoretical Soundness Analysis}

\subsubsection{Strengths of GUIDE Approach}
\begin{itemize}
    \item \textbf{Stochastic Framework}: Pro<PERSON>ly handles random fading channels through probabilistic analysis
    \item \textbf{CRB Foundation}: Uses established Cramér-Rao bounds as fundamental estimation limits
    \item \textbf{Practical Relevance}: Addresses real-world scenarios with channel uncertainty
    \item \textbf{Joint Consideration}: Attempts to unify sensing and communication security metrics
\end{itemize}

\subsubsection{Identified Limitations}
\begin{itemize}
    \item \textbf{Simplified Signal Model}: Uses basic superposition without full FD-ISAC coupling
    \item \textbf{Independence Assumption}: Assumes sensing and communication are independent
    \item \textbf{Single Parameter Focus}: Primarily considers angle estimation for sensing
    \item \textbf{Limited Channel Model}: Restricted to Rayleigh fading scenarios
\end{itemize}

\subsection{Computational Feasibility}

\subsubsection{Complexity Analysis}
\begin{itemize}
    \item \textbf{Individual SOP}: $\mathcal{O}(1)$ - closed-form exponential expressions
    \item \textbf{Joint SOP}: $\mathcal{O}(1)$ - simple arithmetic operations
    \item \textbf{Monte Carlo Alternative}: $\mathcal{O}(N_{MC})$ - for exact simulations
    \item \textbf{Overall}: Computationally efficient for real-time implementation
\end{itemize}

\subsubsection{Implementation Advantages}
\begin{itemize}
    \item \textbf{Fast Evaluation}: Closed-form expressions enable rapid computation
    \item \textbf{Parameter Sensitivity}: Easy to analyze impact of system parameters
    \item \textbf{Design Integration}: Suitable for optimization frameworks
\end{itemize}

\section{Enhanced Derivation Based on Our FD-ISAC Signal Model}

\subsection{Our Established Signal Model}

From our previous work, the eavesdropper receives:
\begin{equation}
\mathbf{y}_{\text{eve}}(l) = \mathbf{G}_E(\theta)\mathbf{x}(l) + \mathbf{H}_{CE}^H\mathbf{x}(l) + \mathbf{z}_{\text{tar}}(l)
\label{eq:our_signal_model}
\end{equation}

where:
\begin{itemize}
    \item $\mathbf{G}_E(\theta) = \beta(l) \mathbf{b}(\theta_E) \mathbf{a}^H(\theta_L)$ - target-reflected sensing channel
    \item $\mathbf{H}_{CE}^H$ - direct communication channel  
    \item $\mathbf{x}(l)$ - FD-ISAC signal vector
    \item Joint parameter vector: $\boldsymbol{\phi} = [\theta_E, \theta_L, \Re(s), \Im(s)]^T$
\end{itemize}

\subsection{Enhanced Joint SOP Derivation}

\subsubsection{Multi-Parameter SOP Framework}

Unlike GUIDE's single-parameter approach, we define outage events for all parameters:

\textbf{Sensing Parameter Outages:}
\begin{align}
\mathcal{O}_{\theta_E}(\epsilon_E) &= \{\text{CRB}(\theta_E) < \epsilon_E\} \\
\mathcal{O}_{\theta_L}(\epsilon_L) &= \{\text{CRB}(\theta_L) < \epsilon_L\}
\end{align}

\textbf{Communication Parameter Outages:}
\begin{align}
\mathcal{O}_{\Re(s)}(\epsilon_c) &= \{\text{CRB}(\Re(s)) < \epsilon_c\} \\
\mathcal{O}_{\Im(s)}(\epsilon_c) &= \{\text{CRB}(\Im(s)) < \epsilon_c\}
\end{align}

\subsubsection{CRB Expressions Under Our Model}

From our joint CRB analysis, under high-SNR approximation:
\begin{align}
\text{CRB}(\theta_E) &\approx \frac{\sigma_z^2}{2|\beta(l)|^2 |\mathbf{a}^H\mathbf{w}|^2 \|\frac{\partial\mathbf{b}}{\partial\theta_E}\|^2} = \frac{1}{K_E \gamma_s} \\
\text{CRB}(\theta_L) &\approx \frac{\sigma_z^2}{2|\beta(l)|^2 \|\mathbf{b}\|^2 |\frac{\partial\mathbf{a}^H}{\partial\theta_L}\mathbf{w}|^2} = \frac{1}{K_L \gamma_s} \\
\text{CRB}(\Re(s)) &= \text{CRB}(\Im(s)) \approx \frac{\sigma_z^2}{2\|\mathbf{H}_{\text{eff}}\mathbf{w}\|^2} = \frac{1}{K_c \gamma_c}
\end{align}

where $\gamma_s = |\beta(l)|^2 P_s/\sigma_z^2$ and $\gamma_c = \|\mathbf{H}_{\text{eff}}\mathbf{w}\|^2/\sigma_z^2$.

\subsubsection{Normalized Fisher Information Coefficients}

The coefficients $K_E$, $K_L$, and $K_c$ are normalized Fisher information factors that simplify the CRB expressions. They are defined as:

\textbf{Eavesdropper Angle Estimation Coefficient:}
\begin{equation}
K_E = \frac{2|\beta(l)|^2 |\mathbf{a}^H\mathbf{w}|^2 \|\frac{\partial\mathbf{b}}{\partial\theta_E}\|^2}{\sigma_z^2}
\label{eq:K_E_definition}
\end{equation}

\textbf{Target Angle Estimation Coefficient:}
\begin{equation}
K_L = \frac{2|\beta(l)|^2 \|\mathbf{b}\|^2 |\frac{\partial\mathbf{a}^H}{\partial\theta_L}\mathbf{w}|^2}{\sigma_z^2}
\label{eq:K_L_definition}
\end{equation}

\textbf{Communication Symbol Estimation Coefficient:}
\begin{equation}
K_c = \frac{2\|\mathbf{H}_{\text{eff}}\mathbf{w}\|^2}{\sigma_z^2}
\label{eq:K_c_definition}
\end{equation}

\textbf{Physical Interpretation:}
\begin{itemize}
    \item $K_E$ depends on the eavesdropper array geometry through $\|\frac{\partial\mathbf{b}}{\partial\theta_E}\|^2$
    \item $K_L$ depends on the BS array geometry through $|\frac{\partial\mathbf{a}^H}{\partial\theta_L}\mathbf{w}|^2$
    \item $K_c$ depends on the effective channel strength $\|\mathbf{H}_{\text{eff}}\mathbf{w}\|^2$
    \item Larger $K$ values indicate better estimation capability (lower security)
\end{itemize}

\textbf{For Uniform Linear Arrays (ULA):}
The steering vector derivatives are:
\begin{align}
\frac{\partial\mathbf{b}(\theta_E)}{\partial\theta_E} &= j\pi\cos(\theta_E) \cdot \text{diag}(0,1,\ldots,N_E-1) \cdot \mathbf{b}(\theta_E) \\
\frac{\partial\mathbf{a}(\theta_L)}{\partial\theta_L} &= j\pi\cos(\theta_L) \cdot \text{diag}(0,1,\ldots,N_{BS}-1) \cdot \mathbf{a}(\theta_L)
\end{align}

This leads to:
\begin{align}
\|\frac{\partial\mathbf{b}}{\partial\theta_E}\|^2 &= \pi^2\cos^2(\theta_E) \sum_{n=0}^{N_E-1} n^2 = \pi^2\cos^2(\theta_E) \frac{(N_E-1)N_E(2N_E-1)}{6} \\
|\frac{\partial\mathbf{a}^H}{\partial\theta_L}\mathbf{w}|^2 &= \pi^2\cos^2(\theta_L) \left|\sum_{n=0}^{N_{BS}-1} n \cdot w_n e^{-j\pi n \sin(\theta_L)}\right|^2
\end{align}

\subsubsection{Numerical Example}

Consider a typical FD-ISAC system with the following parameters:
\begin{itemize}
    \item $N_E = 8$ (eavesdropper antennas), $N_{BS} = 16$ (BS antennas)
    \item $|\beta(l)|^2 = 0.64$ (reflection coefficient magnitude 0.8)
    \item $\theta_E = 45°$, $\theta_L = 30°$ (angles)
    \item $\sigma_z^2 = 1$ (normalized noise power)
    \item Uniform beamforming: $\mathbf{w} = \frac{1}{\sqrt{N_{BS}}} \mathbf{1}_{N_{BS}}$
\end{itemize}

For ULA with half-wavelength spacing:
\begin{align}
K_E &\approx 2 \times 0.64 \times \frac{1}{16} \times \pi^2 \cos^2(45°) \times \frac{7 \times 8 \times 15}{6} \approx 47.1 \\
K_L &\approx 2 \times 0.64 \times 8 \times \pi^2 \cos^2(30°) \times \left|\frac{1}{\sqrt{16}} \sum_{n=0}^{15} n e^{-j\pi n \sin(30°)}\right|^2 \\
K_c &\approx 2 \times \|\mathbf{H}_{\text{eff}}\mathbf{w}\|^2
\end{align}

\textbf{MATLAB Implementation:}
\begin{verbatim}
function [K_E, K_L, K_c] = compute_K_factors(N_E, N_BS, beta, ...
                                           theta_E, theta_L, w, H_eff, sigma_z2)
    % Steering vectors
    b = exp(1j*pi*(0:N_E-1)'*sin(theta_E));
    a = exp(1j*pi*(0:N_BS-1)'*sin(theta_L));

    % Derivatives
    db_dtheta = 1j*pi*cos(theta_E) * diag(0:N_E-1) * b;
    da_dtheta = 1j*pi*cos(theta_L) * diag(0:N_BS-1) * a;

    % K factors
    K_E = 2 * abs(beta)^2 * abs(a'*w)^2 * norm(db_dtheta)^2 / sigma_z2;
    K_L = 2 * abs(beta)^2 * norm(b)^2 * abs(da_dtheta'*w)^2 / sigma_z2;
    K_c = 2 * norm(H_eff*w)^2 / sigma_z2;
end
\end{verbatim}

\subsubsection{Individual Outage Probabilities}

Under Rayleigh fading for both sensing and communication channels:

\textbf{Sensing SOPs:}
\begin{align}
P_{\text{SOP}}^{(\theta_E)} &= P\left(\frac{1}{K_E \gamma_s} < \epsilon_E\right) = \exp\left(-\frac{\lambda_s}{K_E \epsilon_E}\right) \\
P_{\text{SOP}}^{(\theta_L)} &= P\left(\frac{1}{K_L \gamma_s} < \epsilon_L\right) = \exp\left(-\frac{\lambda_s}{K_L \epsilon_L}\right)
\end{align}

\textbf{Communication SOPs:}
\begin{equation}
P_{\text{SOP}}^{(\text{comm})} = P\left(\frac{1}{K_c \gamma_c} < \epsilon_c\right) = \exp\left(-\frac{\lambda_c}{K_c \epsilon_c}\right)
\end{equation}

\subsection{Enhanced Joint SOP Metric}

\subsubsection{Hierarchical Joint SOP Structure}

We propose a three-level hierarchy:

\textbf{Level 1 - Domain-Specific Joint SOPs:}
\begin{align}
\text{JSOP}_{\text{sensing}} &= P_{\text{SOP}}^{(\theta_E)} + P_{\text{SOP}}^{(\theta_L)} - P_{\text{SOP}}^{(\theta_E)} \cdot P_{\text{SOP}}^{(\theta_L)} \\
\text{JSOP}_{\text{comm}} &= 2P_{\text{SOP}}^{(\text{comm})} - (P_{\text{SOP}}^{(\text{comm})})^2
\end{align}

\textbf{Level 2 - Cross-Domain Joint SOP:}
\begin{equation}
\text{JSOP}_{\text{cross}} = \text{JSOP}_{\text{sensing}} + \text{JSOP}_{\text{comm}} - \text{JSOP}_{\text{sensing}} \cdot \text{JSOP}_{\text{comm}}
\end{equation}

\textbf{Level 3 - Weighted Unified SOP:}
\begin{equation}
\text{USOP} = w_s \cdot \text{JSOP}_{\text{sensing}} + w_c \cdot \text{JSOP}_{\text{comm}} + w_{cross} \cdot \text{JSOP}_{\text{cross}}
\end{equation}

\subsubsection{Coupling-Aware Enhancement}

To address GUIDE's independence limitation, we incorporate parameter coupling:

\textbf{Coupling Factor:}
\begin{equation}
\rho_{\text{coupling}} = \frac{|\text{det}(\mathbf{J}_{\text{off-diag}})|}{|\text{det}(\mathbf{J}_{\text{diag}})|}
\end{equation}

where $\mathbf{J}_{\text{off-diag}}$ contains off-diagonal FIM elements and $\mathbf{J}_{\text{diag}}$ contains only diagonal elements.

\textbf{Coupling-Corrected Joint SOP:}
\begin{equation}
\text{JSOP}_{\text{corrected}} = \text{JSOP}_{\text{cross}} \cdot (1 + \alpha \cdot \rho_{\text{coupling}})
\end{equation}

where $\alpha$ is a coupling sensitivity parameter.

\subsection{Design Implications of K Factors}

The K factors provide important insights for secure ISAC system design:

\subsubsection{Security Enhancement Strategies}

\textbf{To Reduce $K_E$ (Improve Sensing Security):}
\begin{itemize}
    \item \textbf{Null-steering beamforming}: Design $\mathbf{w}$ such that $|\mathbf{a}^H\mathbf{w}|^2$ is minimized
    \item \textbf{Artificial noise injection}: Increase effective $\sigma_z^2$ at eavesdropper
    \item \textbf{Array geometry optimization}: Choose antenna spacing to reduce $\|\frac{\partial\mathbf{b}}{\partial\theta_E}\|^2$
\end{itemize}

\textbf{To Reduce $K_L$ (Improve Target Location Security):}
\begin{itemize}
    \item \textbf{Directional beamforming}: Minimize $|\frac{\partial\mathbf{a}^H}{\partial\theta_L}\mathbf{w}|^2$ in eavesdropper direction
    \item \textbf{Power allocation}: Reduce sensing power $|\beta(l)|^2$ when security is critical
    \item \textbf{Frequency diversity}: Use multiple frequencies to decorrelate estimation
\end{itemize}

\textbf{To Reduce $K_c$ (Improve Communication Security):}
\begin{itemize}
    \item \textbf{Precoding design}: Minimize $\|\mathbf{H}_{\text{eff}}\mathbf{w}\|^2$ through careful precoder selection
    \item \textbf{Cooperative jamming}: Use friendly jammers to increase effective noise
    \item \textbf{Channel randomization}: Exploit channel variations to reduce average $K_c$
\end{itemize}

\subsubsection{Trade-off Analysis}

The K factors reveal fundamental trade-offs in ISAC systems:
\begin{align}
\text{Sensing Performance} &\propto K_E, K_L \quad \text{(higher is better)} \\
\text{Communication Performance} &\propto K_c \quad \text{(higher is better)} \\
\text{Security} &\propto \frac{1}{K_E}, \frac{1}{K_L}, \frac{1}{K_c} \quad \text{(lower K is better)}
\end{align}

This creates a three-way trade-off between sensing performance, communication performance, and security.

\section{Comparison: GUIDE vs Enhanced Approach}

\subsection{Theoretical Improvements}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|}
\hline
\textbf{Aspect} & \textbf{GUIDE} & \textbf{Enhanced} \\
\hline
Parameter Coverage & Single ($\theta_E$) & Multiple ($\theta_E, \theta_L, s$) \\
Signal Model & Simplified & Full FD-ISAC \\
Coupling Consideration & Independent & Coupling-aware \\
Channel Model & Rayleigh only & Extensible \\
Hierarchy & Flat & Multi-level \\
\hline
\end{tabular}
\caption{Comparison of GUIDE and Enhanced Approaches}
\end{table}

\subsection{Computational Complexity}

\begin{itemize}
    \item \textbf{GUIDE}: $\mathcal{O}(1)$ - very simple
    \item \textbf{Enhanced}: $\mathcal{O}(N^2)$ - still efficient for practical $N$
    \item \textbf{Trade-off}: Slight complexity increase for significant capability enhancement
\end{itemize}

\section{Implementation Recommendations}

\subsection{Practical Implementation Strategy}

\begin{enumerate}
    \item \textbf{Phase 1}: Implement GUIDE approach for baseline comparison
    \item \textbf{Phase 2}: Develop enhanced multi-parameter version
    \item \textbf{Phase 3}: Add coupling-aware corrections
    \item \textbf{Phase 4}: Validate through simulation and optimization
\end{enumerate}

\subsection{Design Guidelines}

\subsubsection{Threshold Selection}
\begin{itemize}
    \item \textbf{Sensing}: $\epsilon_E = \epsilon_L = \sigma_{\text{angle}}^2$ (required angle accuracy)
    \item \textbf{Communication}: $\epsilon_c = 1/(2\gamma_{\text{req}})$ (required SNR for target SER)
\end{itemize}

\subsubsection{Weight Configuration}
\begin{itemize}
    \item \textbf{Sensing-critical}: $w_s = 0.5$, $w_c = 0.3$, $w_{cross} = 0.2$
    \item \textbf{Communication-critical}: $w_s = 0.3$, $w_c = 0.5$, $w_{cross} = 0.2$
    \item \textbf{Balanced}: $w_s = w_c = 0.4$, $w_{cross} = 0.2$
\end{itemize}

\section{Conclusion and Feasibility Assessment}

\subsection{Overall Feasibility: ⭐⭐⭐⭐⭐ (5/5)}

The GUIDE SOP approach is \textbf{highly feasible} with the following assessment:

\subsubsection{Strengths}
\begin{itemize}
    \item \textbf{Solid theoretical foundation}: Based on established CRB and outage theory
    \item \textbf{Computational efficiency}: Closed-form expressions enable real-time use
    \item \textbf{Practical relevance}: Addresses real-world fading channel scenarios
    \item \textbf{Enhancement potential}: Can be significantly improved with our model
\end{itemize}

\subsubsection{Recommended Enhancements}
\begin{itemize}
    \item \textbf{Multi-parameter extension}: Include all joint parameters
    \item \textbf{Coupling consideration}: Account for parameter interdependencies  
    \item \textbf{Hierarchical structure}: Enable flexible priority weighting
    \item \textbf{Model integration}: Use our established FD-ISAC signal model
\end{itemize}

\subsection{Implementation Priority}

\textbf{High Priority}: The enhanced GUIDE SOP approach should be implemented as it provides:
\begin{enumerate}
    \item Comprehensive security assessment
    \item Computational efficiency
    \item Design optimization capability
    \item Real-world applicability
\end{enumerate}

The combination of GUIDE's stochastic framework with our detailed signal model creates a powerful and practical security metric for FD-ISAC systems.

\end{document}
