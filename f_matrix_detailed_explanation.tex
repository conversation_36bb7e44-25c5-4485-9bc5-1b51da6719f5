\documentclass[journal]{IEEEtran}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{xcolor}

\newcommand{\bm}[1]{\boldsymbol{#1}}

\title{Detailed Analysis of Precoding Matrix F in FD-ISAC Systems}
\author{Precoding Matrix Structure and Design}

\begin{document}
\maketitle

\begin{abstract}
This document provides a comprehensive analysis of the precoding matrix F in full-duplex integrated sensing and communication (FD-ISAC) systems, explaining its partitioned structure for serving both communication users and sensing functions while considering potential eavesdropping threats.
\end{abstract}

\section{Precoding Matrix F: Complete Structure}

\subsection{Overall Matrix Dimensions and Partitioning}

Based on the FD\_dual\_secure\_ISAC\_mono.pdf file, the precoding matrix $\mathbf{F}$ has the following structure:

\begin{equation}
\mathbf{F} = [\mathbf{F}_C \mid \mathbf{F}_S] \in \mathbb{C}^{N_{BS} \times N_D}
\label{eq:f_matrix_partition}
\end{equation}

where:
\begin{itemize}
    \item $N_{BS}$ is the number of base station antennas
    \item $N_D = K + N_S$ is the total number of data streams
    \item $K$ is the number of single-antenna communication users
    \item $N_S$ is the number of sensing streams
\end{itemize}

\subsection{Communication Precoding Submatrix $\mathbf{F}_C$}

The communication precoding submatrix is:
\begin{equation}
\mathbf{F}_C = [\mathbf{f}_1, \mathbf{f}_2, \ldots, \mathbf{f}_K] \in \mathbb{C}^{N_{BS} \times K}
\label{eq:f_c_structure}
\end{equation}

where each column $\mathbf{f}_k \in \mathbb{C}^{N_{BS} \times 1}$ is the precoding vector for the $k$-th single-antenna communication user.

\subsubsection{Individual User Precoders}

Each precoding vector $\mathbf{f}_k$ is designed to:
\begin{itemize}
    \item \textbf{Maximize signal power} at the intended user $k$
    \item \textbf{Minimize interference} to other users
    \item \textbf{Reduce information leakage} to the eavesdropper
    \item \textbf{Satisfy power constraints}
\end{itemize}

The design optimization problem for user $k$ can be formulated as:
\begin{align}
\max_{\mathbf{f}_k} \quad &|h_{C,k}^H \mathbf{f}_k|^2 \\
\text{s.t.} \quad &\|\mathbf{f}_k\|^2 \leq P_k \\
&|\mathbf{h}_{E,k}^H \mathbf{f}_k|^2 \leq \epsilon_k
\end{align}

where:
\begin{itemize}
    \item $h_{C,k}$ is the channel from BS to user $k$
    \item $\mathbf{h}_{E,k}$ represents the effective eavesdropper channel for user $k$
    \item $P_k$ is the power constraint for user $k$
    \item $\epsilon_k$ is the information leakage constraint
\end{itemize}

\subsection{Sensing Precoding Submatrix $\mathbf{F}_S$}

The sensing precoding submatrix is:
\begin{equation}
\mathbf{F}_S = [\mathbf{f}_{S,1}, \mathbf{f}_{S,2}, \ldots, \mathbf{f}_{S,N_S}] \in \mathbb{C}^{N_{BS} \times N_S}
\label{eq:f_s_structure}
\end{equation}

\subsubsection{Sensing Stream Design}

Each sensing precoding vector $\mathbf{f}_{S,i}$ is designed to:
\begin{itemize}
    \item \textbf{Maximize target illumination} in the direction $\theta_L$
    \item \textbf{Minimize sensing information leakage} to the eavesdropper
    \item \textbf{Provide spatial diversity} for robust target detection
    \item \textbf{Maintain orthogonality} with communication streams (if required)
\end{itemize}

\subsection{Complete Transmitted Signal}

The transmitted signal becomes:
\begin{align}
\mathbf{x}(l) &= \mathbf{F}\mathbf{s}(l) \\
&= [\mathbf{F}_C \mid \mathbf{F}_S] \begin{bmatrix} \mathbf{s}_C(l) \\ \mathbf{s}_S(l) \end{bmatrix} \\
&= \mathbf{F}_C \mathbf{s}_C(l) + \mathbf{F}_S \mathbf{s}_S(l) \\
&= \sum_{k=1}^K \mathbf{f}_k s_{C,k}(l) + \sum_{i=1}^{N_S} \mathbf{f}_{S,i} s_{S,i}(l)
\label{eq:transmitted_signal_expanded}
\end{align}

\section{Design Considerations for Security}

\subsection{Eavesdropper-Aware Design}

Given the presence of a multi-antenna eavesdropper, the precoding matrix design must consider:

\subsubsection{Direct Communication Path}
The eavesdropper receives direct communication signals:
\begin{equation}
\mathbf{y}_{E,\text{direct}} = \mathbf{H}_{CE}^H \mathbf{F}_C \mathbf{s}_C(l)
\label{eq:direct_path}
\end{equation}

\subsubsection{Reflected Sensing Path}
The eavesdropper also receives target-reflected signals:
\begin{equation}
\mathbf{y}_{E,\text{reflected}} = \beta(l) \mathbf{b}(\theta_E) \mathbf{a}^H(\theta_L) (\mathbf{F}_C \mathbf{s}_C(l) + \mathbf{F}_S \mathbf{s}_S(l))
\label{eq:reflected_path}
\end{equation}

\subsection{Joint Optimization Framework}

The secure precoding design can be formulated as:
\begin{align}
\min_{\mathbf{F}_C, \mathbf{F}_S} \quad &\mathcal{L}_{\text{total}}(\mathbf{F}_C, \mathbf{F}_S) \\
\text{s.t.} \quad &\text{SINR}_k \geq \Gamma_k, \quad k = 1, \ldots, K \\
&\text{SNR}_{\text{sensing}} \geq \Gamma_S \\
&\|\mathbf{F}\|_F^2 \leq P_{\text{total}}
\end{align}

where $\mathcal{L}_{\text{total}}$ is the total information leakage metric.

\section{Specific Design Examples}

\subsection{Zero-Forcing Communication Precoding}

For communication users, a zero-forcing approach can be used:
\begin{equation}
\mathbf{F}_C = \mathbf{H}_C^H (\mathbf{H}_C \mathbf{H}_C^H)^{-1} \mathbf{P}_C^{1/2}
\label{eq:zf_precoding}
\end{equation}

where $\mathbf{H}_C = [h_{C,1}, h_{C,2}, \ldots, h_{C,K}]^H$ and $\mathbf{P}_C$ is the power allocation matrix.

\subsection{Beamforming for Sensing}

For sensing, directional beamforming toward the target can be employed:
\begin{equation}
\mathbf{f}_{S,1} = \sqrt{P_S} \frac{\mathbf{a}(\theta_L)}{\|\mathbf{a}(\theta_L)\|}
\label{eq:sensing_beamforming}
\end{equation}

\subsection{Artificial Noise Integration}

Additional columns can be added to $\mathbf{F}_S$ for artificial noise generation:
\begin{equation}
\mathbf{F}_S = [\mathbf{f}_{S,\text{signal}} \mid \mathbf{F}_{AN}]
\label{eq:an_integration}
\end{equation}

where $\mathbf{F}_{AN}$ is designed to degrade eavesdropper performance while minimally affecting legitimate users.

\section{Mathematical Properties}

\subsection{Orthogonality Constraints}

In some designs, orthogonality between communication and sensing may be enforced:
\begin{equation}
\mathbf{F}_C^H \mathbf{F}_S = \mathbf{0}_{K \times N_S}
\label{eq:orthogonality}
\end{equation}

\subsection{Power Allocation}

The total transmit power is distributed as:
\begin{equation}
P_{\text{total}} = \|\mathbf{F}_C\|_F^2 + \|\mathbf{F}_S\|_F^2 = \sum_{k=1}^K \|\mathbf{f}_k\|^2 + \sum_{i=1}^{N_S} \|\mathbf{f}_{S,i}\|^2
\label{eq:power_allocation}
\end{equation}

\subsection{Rank Properties}

The rank of the precoding matrix determines the spatial degrees of freedom:
\begin{align}
\text{rank}(\mathbf{F}) &\leq \min(N_{BS}, N_D) \\
\text{rank}(\mathbf{F}_C) &\leq \min(N_{BS}, K) \\
\text{rank}(\mathbf{F}_S) &\leq \min(N_{BS}, N_S)
\end{align}

\section{Impact on Information Leakage}

\subsection{Communication Information Leakage}

The communication mutual information at the eavesdropper depends on $\mathbf{F}_C$:
\begin{equation}
I_C = \log_2 \det\left(\mathbf{I}_K + \frac{1}{N_{CPI} \sigma_n^2} \mathbf{F}_C^H \mathbf{H}_{eff}^H \mathbf{H}_{eff} \mathbf{F}_C\right)
\label{eq:comm_mi_f_dependence}
\end{equation}

\subsection{Sensing Information Leakage}

The sensing mutual information depends on the overall precoding structure:
\begin{equation}
I_S = f(\mathbf{F}\mathbf{F}^H, \mathbf{H}_{eff})
\label{eq:sensing_mi_f_dependence}
\end{equation}

\subsection{Cross-Coupling Effects}

The Fisher Information Matrix cross-terms depend on both $\mathbf{F}_C$ and $\mathbf{F}_S$:
\begin{equation}
\text{FIM}_{i,j} = g(\mathbf{F}_C, \mathbf{F}_S, \mathbf{H}_{eff}, \boldsymbol{\theta})
\label{eq:fim_f_dependence}
\end{equation}

\section{Design Guidelines}

\subsection{For Communication Security}

\begin{enumerate}
    \item \textbf{Null-steering}: Design $\mathbf{F}_C$ to create nulls toward the eavesdropper
    \item \textbf{Power minimization}: Minimize $\|\mathbf{H}_{CE}^H \mathbf{F}_C\|_F^2$
    \item \textbf{Interference exploitation}: Use constructive interference at legitimate users
\end{enumerate}

\subsection{For Sensing Security}

\begin{enumerate}
    \item \textbf{Directional sensing}: Focus sensing power toward the target
    \item \textbf{Waveform diversity}: Use multiple orthogonal sensing waveforms
    \item \textbf{Adaptive beamforming}: Adjust sensing beams based on eavesdropper location
\end{enumerate}

\subsection{For Joint Optimization}

\begin{enumerate}
    \item \textbf{Iterative design}: Alternate between communication and sensing precoder optimization
    \item \textbf{Convex relaxation}: Use semidefinite programming for non-convex problems
    \item \textbf{Machine learning}: Employ deep learning for complex optimization landscapes
\end{enumerate}

\section{Conclusion}

The precoding matrix $\mathbf{F}$ in FD-ISAC systems has a sophisticated partitioned structure that must carefully balance communication performance, sensing effectiveness, and security considerations. The design of both $\mathbf{F}_C$ and $\mathbf{F}_S$ significantly impacts the information leakage to malicious eavesdroppers and requires joint optimization approaches that consider the coupling between sensing and communication functions.

Understanding this structure is crucial for developing secure ISAC systems that can maintain operational effectiveness while protecting against sophisticated eavesdropping attacks.

\end{document}
