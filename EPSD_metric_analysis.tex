\documentclass{article}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{algorithm}
\usepackage{algorithmic}

% Math commands
\newcommand{\bm}[1]{\boldsymbol{#1}}
\newcommand{\tr}{\text{tr}}
\newcommand{\diag}{\text{diag}}
\newcommand{\rank}{\text{rank}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}

\title{Entropic Privacy-Secrecy Divergence (EPSD) for Monostatic ISAC Systems}
\author{Advanced Security Metrics Framework}
\date{\today}

\begin{document}
\maketitle

\begin{abstract}
This document presents a comprehensive analysis of the Entropic Privacy-Secrecy Divergence (EPSD) metric for monostatic integrated sensing and communication (ISAC) systems. The EPSD metric provides a unified framework for evaluating both communication secrecy and sensing privacy while capturing the cross-domain information leakage through entropic measures and KL divergence. We derive the metric based on our established monostatic ISAC signal model and provide practical computation methods.
\end{abstract}

\section{Introduction}

The Entropic Privacy-Secrecy Divergence (EPSD) represents a novel approach to security evaluation in ISAC systems by combining:
\begin{itemize}
    \item KL divergence to measure how secure posteriors diverge from insecure priors
    \item Entropy measures to quantify uncertainty in both domains
    \item Exponential penalty for cross-domain information coupling
\end{itemize}

This metric addresses the fundamental challenge that traditional separate security metrics fail to capture the correlation between sensing and communication information leakage.

\section{EPSD Metric Definition}

\subsection{Core Components}

Based on our monostatic ISAC signal model, we define the following distributions:

\subsubsection{Communication Security Components}
\begin{itemize}
    \item $P(\bm{s}|\bm{y}_{\text{eve}})$: Posterior distribution of communication symbols $\bm{s}$ given eavesdropper's received signal $\bm{y}_{\text{eve}}$
    \item $P_0(\bm{s})$: Prior (insecure baseline) distribution for communication, assuming no secrecy
\end{itemize}

\subsubsection{Sensing Privacy Components}
\begin{itemize}
    \item $Q(\theta_L|\bm{y}_{\text{eve}})$: Posterior distribution of target angle $\theta_L$ given $\bm{y}_{\text{eve}}$
    \item $Q_0(\theta_L)$: Prior distribution for sensing parameters, assuming no privacy
\end{itemize}

\subsection{EPSD Formulation}

The EPSD metric is defined as:
\begin{align}
\text{EPSD} &= D_{\text{KL}}(P(\bm{s}|\bm{y}_{\text{eve}}) \| P_0(\bm{s})) \cdot D_{\text{KL}}(Q(\theta_L|\bm{y}_{\text{eve}}) \| Q_0(\theta_L)) \nonumber \\
&\quad \cdot \exp\left(-H(\bm{s}, \theta_L|\bm{y}_{\text{eve}}) + H(\bm{s}|\bm{y}_{\text{eve}}) + H(\theta_L|\bm{y}_{\text{eve}})\right)
\end{align}

where:
\begin{itemize}
    \item $D_{\text{KL}}(A \| B) = \sum A \log \frac{A}{B}$ is the KL divergence
    \item $H(\cdot)$ denotes entropy
    \item The exponential term captures mutual information-like coupling: $-I(\bm{s}, \theta_L|\bm{y}_{\text{eve}})$
\end{itemize}

\section{Derivation for Monostatic ISAC System}

\subsection{Signal Model Recap}

From our established monostatic ISAC model:
\begin{align}
\bm{y}_{\text{eve}}(l) &= \bm{G}_E(\theta_L)\bm{x}(l) + \bm{H}_{CE}\bm{x}(l) + \bm{z}_{\text{tar}}(l) \\
&= [\bm{G}_E(\theta_L) + \bm{H}_{CE}]\bm{x}(l) + \bm{z}_{\text{tar}}(l) \\
&= \bm{H}_{\text{eff}}(\theta_L)\bm{x}(l) + \bm{z}_{\text{tar}}(l)
\end{align}

where $\bm{H}_{\text{eff}}(\theta_L) = \bm{G}_E(\theta_L) + \bm{H}_{CE}$ is the effective eavesdropping channel.

\subsection{Posterior Distributions}

\subsubsection{Communication Posterior}

Given the linear Gaussian model, the posterior distribution of communication symbols is:
\begin{equation}
P(\bm{x}|\bm{y}_{\text{eve}}, \theta_L) = \mathcal{CN}(\bm{\mu}_x, \bm{\Sigma}_x)
\end{equation}

where:
\begin{align}
\bm{\Sigma}_x &= \left(\bm{Q}_x^{-1} + \frac{1}{\sigma^2_{\text{tar}}}\bm{H}_{\text{eff}}^H(\theta_L)\bm{H}_{\text{eff}}(\theta_L)\right)^{-1} \\
\bm{\mu}_x &= \frac{1}{\sigma^2_{\text{tar}}}\bm{\Sigma}_x \bm{H}_{\text{eff}}^H(\theta_L)\bm{y}_{\text{eve}}
\end{align}

\subsubsection{Sensing Posterior}

For the target angle, using Bayesian estimation:
\begin{equation}
Q(\theta_L|\bm{y}_{\text{eve}}) \propto p(\bm{y}_{\text{eve}}|\theta_L) Q_0(\theta_L)
\end{equation}

The likelihood function is:
\begin{equation}
p(\bm{y}_{\text{eve}}|\theta_L) = \frac{1}{(\pi\sigma^2_{\text{tar}})^{N_E}} \exp\left(-\frac{1}{\sigma^2_{\text{tar}}}\|\bm{y}_{\text{eve}} - \bm{H}_{\text{eff}}(\theta_L)\bm{\mu}_x\|^2\right)
\end{equation}

\subsection{KL Divergence Components}

\subsubsection{Communication KL Divergence}

For Gaussian distributions:
\begin{align}
D_{\text{KL}}(P(\bm{x}|\bm{y}_{\text{eve}}) \| P_0(\bm{x})) &= \frac{1}{2}\left[\tr(\bm{Q}_x^{-1}\bm{\Sigma}_x) - N_{BS} + \log\frac{\det(\bm{Q}_x)}{\det(\bm{\Sigma}_x)} \right. \\
&\quad \left. + \bm{\mu}_x^H\bm{Q}_x^{-1}\bm{\mu}_x\right]
\end{align}

where $P_0(\bm{x}) = \mathcal{CN}(\bm{0}, \bm{Q}_x)$ is the prior distribution.

\subsubsection{Sensing KL Divergence}

For the angle parameter:
\begin{equation}
D_{\text{KL}}(Q(\theta_L|\bm{y}_{\text{eve}}) \| Q_0(\theta_L)) = \int Q(\theta_L|\bm{y}_{\text{eve}}) \log\frac{Q(\theta_L|\bm{y}_{\text{eve}})}{Q_0(\theta_L)} d\theta_L
\end{equation}

\subsection{Entropy Terms}

\subsubsection{Individual Entropies}

Communication entropy:
\begin{equation}
H(\bm{x}|\bm{y}_{\text{eve}}) = \frac{1}{2}\log\det(2\pi e \bm{\Sigma}_x)
\end{equation}

Sensing entropy (approximated for Gaussian posterior):
\begin{equation}
H(\theta_L|\bm{y}_{\text{eve}}) \approx \frac{1}{2}\log(2\pi e \cdot \text{CRB}_{\text{eve}}(\theta_L))
\end{equation}

\subsubsection{Joint Entropy}

The joint entropy captures the correlation:
\begin{equation}
H(\bm{x}, \theta_L|\bm{y}_{\text{eve}}) = H(\bm{x}|\bm{y}_{\text{eve}}) + H(\theta_L|\bm{x}, \bm{y}_{\text{eve}})
\end{equation}

\section{Practical Computation Methods}

\subsection{Algorithm for EPSD Computation}

\begin{algorithm}
\caption{EPSD Computation for Monostatic ISAC}
\begin{algorithmic}[1]
\REQUIRE System parameters: $\bm{H}_{CE}$, $\bm{G}_E(\theta_L)$, $\bm{Q}_x$, $\sigma^2_{\text{tar}}$
\REQUIRE Received signal: $\bm{y}_{\text{eve}}$
\ENSURE EPSD value

\STATE Compute effective channel: $\bm{H}_{\text{eff}}(\theta_L) = \bm{G}_E(\theta_L) + \bm{H}_{CE}$

\STATE Compute communication posterior parameters:
\STATE $\bm{\Sigma}_x = \left(\bm{Q}_x^{-1} + \frac{1}{\sigma^2_{\text{tar}}}\bm{H}_{\text{eff}}^H\bm{H}_{\text{eff}}\right)^{-1}$
\STATE $\bm{\mu}_x = \frac{1}{\sigma^2_{\text{tar}}}\bm{\Sigma}_x \bm{H}_{\text{eff}}^H\bm{y}_{\text{eve}}$

\STATE Compute sensing posterior via Bayesian update:
\STATE $Q(\theta_L|\bm{y}_{\text{eve}}) \propto p(\bm{y}_{\text{eve}}|\theta_L) Q_0(\theta_L)$

\STATE Calculate KL divergences:
\STATE $D_{\text{KL}}^{(c)} = D_{\text{KL}}(P(\bm{x}|\bm{y}_{\text{eve}}) \| P_0(\bm{x}))$
\STATE $D_{\text{KL}}^{(s)} = D_{\text{KL}}(Q(\theta_L|\bm{y}_{\text{eve}}) \| Q_0(\theta_L))$

\STATE Calculate entropy terms:
\STATE $H_c = H(\bm{x}|\bm{y}_{\text{eve}})$, $H_s = H(\theta_L|\bm{y}_{\text{eve}})$, $H_{cs} = H(\bm{x}, \theta_L|\bm{y}_{\text{eve}})$

\STATE Compute EPSD:
\STATE $\text{EPSD} = D_{\text{KL}}^{(c)} \cdot D_{\text{KL}}^{(s)} \cdot \exp(-(H_{cs} - H_c - H_s))$

\RETURN EPSD
\end{algorithmic}
\end{algorithm}

\subsection{Approximation Methods}

\subsubsection{Monte Carlo Estimation}

For complex posterior distributions:
\begin{enumerate}
    \item Sample from $P(\bm{x}|\bm{y}_{\text{eve}})$ using MCMC
    \item Approximate $Q(\theta_L|\bm{y}_{\text{eve}})$ via particle filtering
    \item Estimate entropies using sample-based methods
\end{enumerate}

\subsubsection{Gaussian Approximation}

For computational efficiency:
\begin{enumerate}
    \item Approximate sensing posterior as Gaussian: $Q(\theta_L|\bm{y}_{\text{eve}}) \approx \mathcal{N}(\hat{\theta}_L, \text{CRB}_{\text{eve}}(\theta_L))$
    \item Use closed-form expressions for Gaussian KL divergences
    \item Apply copula models for joint entropy estimation
\end{enumerate}

\section{EPSD Properties and Interpretation}

\subsection{Metric Properties}

\begin{enumerate}
    \item \textbf{Non-negativity}: EPSD $\geq 0$, with higher values indicating better security
    \item \textbf{Coupling Sensitivity}: The exponential term heavily penalizes cross-domain correlation
    \item \textbf{Individual Security}: Each KL divergence term measures domain-specific security
    \item \textbf{Unified Framework}: Single metric captures both sensing and communication security
\end{enumerate}

\subsection{Physical Interpretation}

\begin{itemize}
    \item \textbf{High EPSD}: Strong divergence from insecure baselines in both domains, low cross-correlation
    \item \textbf{Low EPSD}: Either poor individual security or high cross-domain information leakage
    \item \textbf{Exponential Penalty}: Promotes designs that decorrelate sensing and communication information
\end{itemize}

\subsection{Comparison with Existing Metrics}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|}
\hline
Metric & Cross-Domain & Unified & Theoretical Foundation \\
\hline
WSI & No & Yes & Information Theory \\
JSI & Yes & Yes & Estimation Theory \\
EPSD & Yes & Yes & Information Theory + Bayesian \\
\hline
\end{tabular}
\caption{Comparison of security metrics}
\end{table}

\section{Optimization Framework}

\subsection{EPSD-Based Optimization}

The security optimization problem becomes:
\begin{align}
\max_{\bm{Q}_x} \quad & \text{EPSD}(\bm{Q}_x) \\
\text{s.t.} \quad & \tr(\bm{Q}_x) \leq P_{\max} \\
& \text{SINR}_k \geq \Gamma_k, \quad \forall k \\
& \text{CRB}_{\text{BS}}(\theta_L) \leq \gamma_{\text{sense}} \\
& \bm{Q}_x \succeq 0
\end{align}

\subsection{Gradient-Based Optimization}

The gradient of EPSD with respect to $\bm{Q}_x$ involves:
\begin{align}
\frac{\partial \text{EPSD}}{\partial \bm{Q}_x} &= \frac{\partial D_{\text{KL}}^{(c)}}{\partial \bm{Q}_x} \cdot D_{\text{KL}}^{(s)} \cdot \exp(\cdot) \\
&\quad + D_{\text{KL}}^{(c)} \cdot \frac{\partial D_{\text{KL}}^{(s)}}{\partial \bm{Q}_x} \cdot \exp(\cdot) \\
&\quad + D_{\text{KL}}^{(c)} \cdot D_{\text{KL}}^{(s)} \cdot \exp(\cdot) \cdot \frac{\partial}{\partial \bm{Q}_x}(\text{entropy terms})
\end{align}

\section{Numerical Examples and Validation}

\subsection{System Parameters}

Consider a monostatic ISAC system with:
\begin{itemize}
    \item $N_{BS} = 8$ antennas at base station
    \item $N_E = 4$ antennas at eavesdropper  
    \item $K = 2$ communication users
    \item Target at angle $\theta_L = \pi/6$
\end{itemize}

\subsection{EPSD Evaluation}

Preliminary analysis shows:
\begin{enumerate}
    \item EPSD increases with signal power allocation to decorrelated beamforming
    \item Cross-domain penalty effectively promotes security-oriented designs
    \item Computational complexity is manageable for real-time applications
\end{enumerate}

\section{Conclusions and Future Work}

\subsection{Key Contributions}

\begin{enumerate}
    \item Derived EPSD metric for monostatic ISAC systems based on established signal model
    \item Provided practical computation algorithms with approximation methods
    \item Demonstrated the metric's ability to capture cross-domain security coupling
    \item Established optimization framework for EPSD-based secure design
\end{enumerate}

\subsection{Future Directions}

\begin{enumerate}
    \item Extend to multi-target and multi-eavesdropper scenarios
    \item Develop efficient approximation algorithms for real-time implementation
    \item Investigate EPSD-based adaptive beamforming strategies
    \item Compare performance with existing security metrics through simulations
\end{enumerate}

The EPSD metric represents a significant advancement in unified security evaluation for ISAC systems, providing both theoretical rigor and practical applicability.

\end{document}
