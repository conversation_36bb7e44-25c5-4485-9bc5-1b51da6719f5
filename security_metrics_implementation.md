# 通感一体化系统安全性能指标实现方案

## 🎯 核心设计理念

基于您的需求，我设计了一套**多层次、多维度**的安全性能指标体系，能够全面评估单站ISAC系统的安全性。

## 📊 指标体系架构

```
系统安全性能指标
├── 单项安全指标
│   ├── 感知安全指标 (Sensing Security)
│   └── 通信安全指标 (Communication Security)
└── 综合安全指标
    ├── 加权安全指数 (WSI)
    ├── 安全效率指标 (SEM)
    └── 统一安全等级 (USL)
```

## 🔍 详细指标设计

### 1. 感知安全指标 (Sensing Secrecy Rate)

**核心思想**: 通过比较窃听者和合法接收机的目标估计精度来量化感知安全性

```latex
R_sense = (1/2) * log2(CRB_eve(θL) / CRB_BS(θL))
```

**物理意义**:
- 值越大 → 窃听者估计精度越差 → 感知越安全
- R_sense = 3 bits 表示窃听者的估计误差是合法接收机的8倍

**优势**:
- 直接反映目标位置信息的保护程度
- 基于信息论，具有理论基础
- 可量化感知隐私保护效果

### 2. 通信安全指标 (Communication Secrecy Rate)

**核心思想**: 使用经典的保密容量概念

```latex
R_comm = [I(x; y_CU) - I(x; y_eve)]^+
```

**物理意义**:
- 正值表示存在安全通信容量
- 值越大 → 安全通信能力越强
- 零值表示无法实现安全通信

### 3. 加权安全指数 (WSI) - **推荐主要指标**

**核心思想**: 将感知和通信安全统一到[0,1]区间内

```latex
WSI = w_s * (R_sense/R_sense_max) + w_c * (R_comm/R_comm_max)
```

**应用场景**:
- **均衡应用**: w_s = w_c = 0.5
- **感知优先**: w_s = 0.7, w_c = 0.3
- **通信优先**: w_s = 0.3, w_c = 0.7

## 🛠️ 实现步骤

### Step 1: 计算基础参数

```matlab
% 1. 计算Fisher信息矩阵
J_BS = compute_fisher_matrix(G_L, Q_x, sigma_BS);
J_eve = compute_fisher_matrix(G_E, Q_x, sigma_tar);

% 2. 计算CRB
CRB_BS = inv(J_BS);
CRB_eve = inv(J_eve);

% 3. 计算互信息
I_CU = log2(det(eye(N) + h_CU * Q_x * h_CU' / sigma_CU^2));
I_eve = log2(det(eye(N_E) + H_eff * Q_x * H_eff' / sigma_tar^2));
```

### Step 2: 计算单项安全指标

```matlab
% 感知安全指标
R_sense = 0.5 * log2(CRB_eve / CRB_BS);

% 通信安全指标  
R_comm = max(0, I_CU - I_eve);
```

### Step 3: 计算综合安全指标

```matlab
% 归一化
R_sense_norm = R_sense / R_sense_max;
R_comm_norm = R_comm / R_comm_max;

% 加权安全指数
WSI = w_s * R_sense_norm + w_c * R_comm_norm;

% 统一安全等级
USL = min(R_sense/R_sense_th, R_comm/R_comm_th);
```

## 📈 指标特性分析

### 1. WSI特性
- **范围**: [0, 1]
- **单调性**: 随安全性提升而增加
- **可解释性**: 0表示无安全性，1表示最佳安全性
- **灵活性**: 可通过权重调整适应不同应用

### 2. USL特性
- **范围**: [0, ∞)
- **保守性**: 反映最薄弱的安全环节
- **阈值导向**: 便于设定安全标准
- **决策友好**: 直观反映是否满足安全要求

## 🎯 应用指导

### 1. 不同应用场景的指标选择

| 应用场景 | 推荐指标 | 权重设置 | 阈值设置 |
|----------|----------|----------|----------|
| 智能交通 | WSI | w_s=0.6, w_c=0.4 | WSI≥0.7 |
| 无人机监控 | USL | - | USL≥1.0 |
| 工业物联网 | WSI | w_s=0.4, w_c=0.6 | WSI≥0.6 |
| 军事通信 | USL | - | USL≥2.0 |

### 2. 系统设计指导

```latex
% 优化目标函数示例
maximize: WSI
subject to:
    tr(Q_x) ≤ P_max
    SINR_k ≥ Γ_k, ∀k
    CRB_BS(θL) ≤ γ_sense
    WSI ≥ WSI_min
```

## 🔧 实际计算示例

### 示例参数
- 基站天线数: N_BS = 8
- 窃听者天线数: N_E = 4  
- 用户数: K = 2
- 信噪比: SNR = 10 dB

### 计算流程
```python
import numpy as np

def calculate_security_metrics(G_L, G_E, H_CE, h_CU, Q_x, noise_params):
    # 1. 计算CRB
    CRB_BS = compute_CRB(G_L, Q_x, noise_params['sigma_BS'])
    CRB_eve = compute_CRB(G_E, Q_x, noise_params['sigma_tar'])
    
    # 2. 计算互信息
    I_CU = compute_mutual_info(h_CU, Q_x, noise_params['sigma_CU'])
    I_eve = compute_mutual_info(G_E + H_CE, Q_x, noise_params['sigma_tar'])
    
    # 3. 计算安全指标
    R_sense = 0.5 * np.log2(CRB_eve / CRB_BS)
    R_comm = max(0, I_CU - I_eve)
    
    # 4. 计算综合指标
    WSI = 0.5 * (R_sense/5) + 0.5 * (R_comm/3)  # 假设最大值
    USL = min(R_sense/3, R_comm/1)  # 假设阈值
    
    return {
        'R_sense': R_sense,
        'R_comm': R_comm, 
        'WSI': WSI,
        'USL': USL
    }
```

## 📊 性能评估建议

### 1. 基准对比
- **无安全措施基准**: 计算无安全设计时的性能
- **理想安全基准**: 计算理论最优安全性能
- **现有方案对比**: 与其他安全方案比较

### 2. 敏感性分析
- 分析指标对系统参数的敏感性
- 评估在不同信道条件下的稳定性
- 研究权重设置对结果的影响

### 3. 实时监控
- 设计在线计算算法
- 建立安全状态监控系统
- 实现自适应安全策略调整

## 🎉 总结

这套指标体系具有以下优势：

1. **全面性**: 覆盖感知和通信两个维度
2. **灵活性**: 可根据应用需求调整权重
3. **实用性**: 便于计算和实时监控
4. **理论性**: 基于信息论和估计理论
5. **可扩展性**: 易于添加新的安全维度

推荐在实际应用中**以WSI为主要指标，USL为辅助指标**，根据具体应用场景调整权重参数。
