function metrics = compute_security_metrics(system_params, channel_params, signal_params)
% COMPUTE_SECURITY_METRICS - 计算通感一体化系统安全性能指标
%
% 输入参数:
%   system_params: 系统参数结构体
%   channel_params: 信道参数结构体  
%   signal_params: 信号参数结构体
%
% 输出:
%   metrics: 安全性能指标结构体

%% 提取参数
N_BS = system_params.N_BS;           % 基站天线数
N_E = system_params.N_E;             % 窃听者天线数
K = system_params.K;                 % 用户数
theta_L = system_params.theta_L;     % 目标角度

% 信道矩阵
G_L = channel_params.G_L;            % 基站目标反射信道
G_E = channel_params.G_E;            % 窃听者目标反射信道
H_CE = channel_params.H_CE;          % 基站到窃听者直接信道
h_CU = channel_params.h_CU;          % 基站到用户信道

% 信号和噪声参数
Q_x = signal_params.Q_x;             % 信号协方差矩阵
sigma_BS = signal_params.sigma_BS;   % 基站噪声方差
sigma_tar = signal_params.sigma_tar; % 窃听者噪声方差
sigma_CU = signal_params.sigma_CU;   % 用户噪声方差

%% 1. 计算感知安全指标

% 计算基站的Fisher信息矩阵
J_BS = compute_fisher_information(G_L, Q_x, sigma_BS, theta_L, N_BS);

% 计算窃听者的Fisher信息矩阵
J_eve = compute_fisher_information(G_E, Q_x, sigma_tar, theta_L, N_E);

% 计算CRB
CRB_BS = 1 / J_BS;
CRB_eve = 1 / J_eve;

% 感知保密率
R_sense = 0.5 * log2(CRB_eve / CRB_BS);

%% 2. 计算通信安全指标

% 合法用户的互信息 (假设单用户)
I_CU = log2(det(eye(size(h_CU,1)) + (h_CU * Q_x * h_CU') / sigma_CU^2));

% 窃听者的有效信道
H_eff = G_E + H_CE;

% 窃听者的互信息
I_eve = log2(det(eye(N_E) + (H_eff * Q_x * H_eff') / sigma_tar^2));

% 通信保密率
R_comm = max(0, I_CU - I_eve);

%% 3. 计算综合安全指标

% 设置归一化参数和权重
R_sense_max = 10;  % 感知保密率最大值 (可调整)
R_comm_max = 5;    % 通信保密率最大值 (可调整)
w_s = 0.5;         % 感知权重
w_c = 0.5;         % 通信权重

% 归一化
R_sense_norm = min(R_sense / R_sense_max, 1);
R_comm_norm = min(R_comm / R_comm_max, 1);

% 加权安全指数 (WSI)
WSI = w_s * R_sense_norm + w_c * R_comm_norm;

% 设置安全阈值
R_sense_th = 3;    % 感知安全阈值
R_comm_th = 1;     % 通信安全阈值

% 统一安全等级 (USL)
USL = min(R_sense / R_sense_th, R_comm / R_comm_th);

% 安全效率指标 (SEM)
% 计算性能代价
perf_cost = compute_performance_cost(CRB_BS, R_comm, system_params);
SEM = WSI / perf_cost;

%% 4. 输出结果
metrics.R_sense = R_sense;
metrics.R_comm = R_comm;
metrics.WSI = WSI;
metrics.USL = USL;
metrics.SEM = SEM;
metrics.CRB_BS = CRB_BS;
metrics.CRB_eve = CRB_eve;
metrics.I_CU = I_CU;
metrics.I_eve = I_eve;

% 安全等级评估
metrics.security_level = evaluate_security_level(WSI, USL);

end

%% 辅助函数

function J = compute_fisher_information(G, Q_x, sigma_n, theta, N_ant)
% 计算Fisher信息矩阵 (简化版本，假设单参数估计)

% 计算导数 (数值方法)
delta_theta = 1e-6;
theta_plus = theta + delta_theta;
theta_minus = theta - delta_theta;

% 重新计算信道矩阵
a_plus = exp(1j * pi * (0:N_ant-1)' * sin(theta_plus));
a_minus = exp(1j * pi * (0:N_ant-1)' * sin(theta_minus));

% 数值导数
da_dtheta = (a_plus - a_minus) / (2 * delta_theta);

% Fisher信息 (简化计算)
J = (2 / sigma_n^2) * real(da_dtheta' * Q_x * da_dtheta);

end

function cost = compute_performance_cost(CRB_BS, R_comm, params)
% 计算性能代价

% 参考性能 (无安全措施时)
CRB_ref = params.CRB_ref;  % 参考CRB
R_comm_ref = params.R_comm_ref;  % 参考通信速率

% 权重参数
alpha = 0.5;
beta = 0.5;

% 性能代价
cost = alpha * (CRB_BS / CRB_ref) + beta * (R_comm_ref / max(R_comm, 0.1));

end

function level = evaluate_security_level(WSI, USL)
% 评估安全等级

if WSI >= 0.8 && USL >= 1.5
    level = 'High';
elseif WSI >= 0.6 && USL >= 1.0
    level = 'Medium';
elseif WSI >= 0.4 && USL >= 0.5
    level = 'Low';
else
    level = 'Insufficient';
end

end

%% 示例使用函数
function example_usage()
% 示例：如何使用安全指标计算函数

% 系统参数
system_params.N_BS = 8;
system_params.N_E = 4;
system_params.K = 2;
system_params.theta_L = pi/6;
system_params.CRB_ref = 1e-4;
system_params.R_comm_ref = 3;

% 生成示例信道
N_BS = system_params.N_BS;
N_E = system_params.N_E;

% 目标反射信道
alpha = 0.8 * exp(1j * pi/4);
a_BS = exp(1j * pi * (0:N_BS-1)' * sin(system_params.theta_L));
channel_params.G_L = alpha * a_BS * a_BS';

% 窃听者信道
beta = 0.6 * exp(1j * pi/3);
theta_E = pi/4;
a_E = exp(1j * pi * (0:N_E-1)' * sin(theta_E));
channel_params.G_E = beta * a_E * a_BS';

% 直接信道
channel_params.H_CE = (randn(N_E, N_BS) + 1j*randn(N_E, N_BS))/sqrt(2);

% 用户信道
channel_params.h_CU = (randn(N_BS, 1) + 1j*randn(N_BS, 1))/sqrt(2);

% 信号参数
signal_params.Q_x = eye(N_BS);  % 单位功率
signal_params.sigma_BS = 0.1;
signal_params.sigma_tar = 0.2;
signal_params.sigma_CU = 0.1;

% 计算安全指标
metrics = compute_security_metrics(system_params, channel_params, signal_params);

% 显示结果
fprintf('=== 安全性能指标结果 ===\n');
fprintf('感知保密率: %.3f bits\n', metrics.R_sense);
fprintf('通信保密率: %.3f bits/s/Hz\n', metrics.R_comm);
fprintf('加权安全指数 (WSI): %.3f\n', metrics.WSI);
fprintf('统一安全等级 (USL): %.3f\n', metrics.USL);
fprintf('安全效率指标 (SEM): %.3f\n', metrics.SEM);
fprintf('安全等级: %s\n', metrics.security_level);

end

%% 批量分析函数
function results = batch_security_analysis(param_ranges)
% 批量分析不同参数下的安全性能

SNR_range = param_ranges.SNR_range;
N_E_range = param_ranges.N_E_range;

results = [];
idx = 1;

for SNR = SNR_range
    for N_E = N_E_range
        % 更新参数
        [sys_params, ch_params, sig_params] = generate_params(SNR, N_E);
        
        % 计算指标
        metrics = compute_security_metrics(sys_params, ch_params, sig_params);
        
        % 保存结果
        results(idx).SNR = SNR;
        results(idx).N_E = N_E;
        results(idx).WSI = metrics.WSI;
        results(idx).USL = metrics.USL;
        results(idx).R_sense = metrics.R_sense;
        results(idx).R_comm = metrics.R_comm;
        
        idx = idx + 1;
    end
end

end

function [sys_params, ch_params, sig_params] = generate_params(SNR, N_E)
% 根据SNR和窃听者天线数生成参数

% 基础系统参数
sys_params.N_BS = 8;
sys_params.N_E = N_E;
sys_params.K = 2;
sys_params.theta_L = pi/6;
sys_params.CRB_ref = 1e-4;
sys_params.R_comm_ref = 3;

% 根据SNR设置噪声方差
noise_power = 10^(-SNR/10);
sig_params.sigma_BS = sqrt(noise_power);
sig_params.sigma_tar = sqrt(noise_power * 2);  % 窃听者噪声更大
sig_params.sigma_CU = sqrt(noise_power);
sig_params.Q_x = eye(sys_params.N_BS);

% 生成信道参数 (简化)
ch_params = generate_channels(sys_params);

end

function ch_params = generate_channels(sys_params)
% 生成信道参数

N_BS = sys_params.N_BS;
N_E = sys_params.N_E;

% 目标反射信道
alpha = 0.8 * exp(1j * pi/4);
a_BS = exp(1j * pi * (0:N_BS-1)' * sin(sys_params.theta_L));
ch_params.G_L = alpha * a_BS * a_BS';

% 窃听者信道
beta = 0.6 * exp(1j * pi/3);
theta_E = pi/4;
a_E = exp(1j * pi * (0:N_E-1)' * sin(theta_E));
ch_params.G_E = beta * a_E * a_BS';

% 直接信道
ch_params.H_CE = (randn(N_E, N_BS) + 1j*randn(N_E, N_BS))/sqrt(2);

% 用户信道
ch_params.h_CU = (randn(N_BS, 1) + 1j*randn(N_BS, 1))/sqrt(2);

end
