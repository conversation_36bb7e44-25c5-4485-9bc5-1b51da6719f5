This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.4.28)  18 JUL 2025 15:17
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**EPSD_metric_analysis
(./EPSD_metric_analysis.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count192
\c@section=\count193
\c@subsection=\count194
\c@subsubsection=\count195
\c@paragraph=\count196
\c@subparagraph=\count197
\c@figure=\count198
\c@table=\count199
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen142
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen143
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count266
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count267
\leftroot@=\count268
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count269
\DOTSCASE@=\count270
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen144
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count271
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count272
\dotsspace@=\muskip17
\c@parentequation=\count273
\dspbrk@lvl=\count274
\tag@help=\toks18
\row@=\count275
\column@=\count276
\maxfields@=\count277
\andhelp@=\toks19
\eqnshift@=\dimen145
\alignsep@=\dimen146
\tagshift@=\dimen147
\tagwidth@=\dimen148
\totwidth@=\dimen149
\lineht@=\dimen150
\@envbody=\toks20
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks22
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen151
\Gin@req@width=\dimen152
) (/usr/local/texlive/2025/texmf-dist/tex/latex/algorithms/algorithm.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (/usr/local/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count278
\float@exts=\toks23
\float@box=\box54
\@float@everytoks=\toks24
\@floatcapt=\box55
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks25
\c@algorithm=\count279
) (/usr/local/texlive/2025/texmf-dist/tex/latex/algorithms/algorithmic.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
\c@ALC@unique=\count280
\c@ALC@line=\count281
\c@ALC@rem=\count282
\c@ALC@depth=\count283
\ALC@tlm=\skip54
\algorithmicindent=\skip55
) (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count284
\l__pdf_internal_box=\box56
\g__pdf_backend_annotation_int=\count285
\g__pdf_backend_link_int=\count286
) (./EPSD_metric_analysis.aux)
\openout1 = `EPSD_metric_analysis.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Trying to load font information for U+msa on input line 21.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 21.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Overfull \hbox (11.06314pt too wide) in paragraph at lines 24--25
[]\TU/lmr/m/n/9 This document presents a comprehensive analysis of the Entropic Privacy-
 []



[1

]
Overfull \hbox (23.3746pt too wide) in paragraph at lines 68--69
[]\TU/lmr/m/n/10 The exponential term captures mutual information-like coupling: $\OMS/cmsy/m/n/10 ^^@\OML/cmm/m/it/10 I\OT1/cmr/m/n/10 ([]\OML/cmm/m/it/10 ; ^^R[]\OMS/cmsy/m/n/10 j[][]\OT1/cmr/m/n/10 )$ 
 []



[2]

[3]
Overfull \hbox (15.68277pt too wide) in paragraph at lines 200--201
[]\TU/lmr/m/n/10 Approximate sensing posterior as Gaussian: $\OML/cmm/m/it/10 Q\OT1/cmr/m/n/10 (\OML/cmm/m/it/10 ^^R[]\OMS/cmsy/m/n/10 j[][]\OT1/cmr/m/n/10 ) \OMS/cmsy/m/n/10 ^^Y N\OT1/cmr/m/n/10 ([]\OML/cmm/m/it/10 ; [][]\OT1/cmr/m/n/10 (\OML/cmm/m/it/10 ^^R[]\OT1/cmr/m/n/10 ))$ 
 []



[4]
Overfull \hbox (1.06728pt too wide) in paragraph at lines 212--213
[]\TU/lmr/bx/n/10 Individual Security\TU/lmr/m/n/10 : Each KL divergence term measures domain-specific
 []



[5]

[6]

[7] (./EPSD_metric_analysis.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
 ) 
Here is how much of TeX's memory you used:
 2555 strings out of 473832
 37462 string characters out of 5729481
 427613 words of memory out of 5000000
 25594 multiletter control sequences out of 15000+600000
 568034 words of font info for 83 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 57i,14n,65p,537b,376s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on EPSD_metric_analysis.pdf (7 pages).
