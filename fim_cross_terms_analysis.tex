\documentclass[journal]{IEEEtran}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}

\newcommand{\bm}[1]{\boldsymbol{#1}}

\title{Analysis of Cross-Terms in Fisher Information Matrix for ISAC Systems}
\author{FIM Cross-Term Analysis}

\begin{document}
\maketitle

\begin{abstract}
This document provides a detailed analysis of the cross-terms (off-diagonal elements) in the Fisher Information Matrix (FIM) for ISAC systems. We examine when these terms are zero, non-zero, and their physical significance in parameter estimation coupling.
\end{abstract}

\section{Fisher Information Matrix Structure}

For the joint parameter vector $\boldsymbol{\phi} = [\theta_E, \theta_L, \Re(s), \Im(s)]^T$, the FIM is:

\begin{equation}
\mathbf{J} = \frac{2}{\sigma_z^2} \Re\left\{\frac{\partial \boldsymbol{\mu}^H}{\partial \boldsymbol{\phi}} \frac{\partial \boldsymbol{\mu}}{\partial \boldsymbol{\phi}^T}\right\}
\end{equation}

where $\boldsymbol{\mu} = \mathbf{H}_{\text{eff}} \mathbf{w} s$ is the mean vector.

\section{Detailed Cross-Term Analysis}

\subsection{Mean Vector Partial Derivatives}

The mean vector is:
\begin{equation}
\boldsymbol{\mu} = (\mathbf{G}_E(\boldsymbol{\theta}) + \mathbf{H}_{CE}^H) \mathbf{w} s = \mathbf{H}_{\text{eff}} \mathbf{w} s
\end{equation}

where $\mathbf{G}_E(\boldsymbol{\theta}) = \beta(l) \mathbf{b}(\theta_E) \mathbf{a}^H(\theta_L)$.

The partial derivatives are:
\begin{align}
\frac{\partial \boldsymbol{\mu}}{\partial \theta_E} &= \beta s (\mathbf{a}^H \mathbf{w}) \frac{\partial \mathbf{b}(\theta_E)}{\partial \theta_E} \label{eq:mu_theta_E}\\
\frac{\partial \boldsymbol{\mu}}{\partial \theta_L} &= \beta s \frac{\partial \mathbf{a}^H(\theta_L)}{\partial \theta_L} \mathbf{w} \mathbf{b}(\theta_E) \label{eq:mu_theta_L}\\
\frac{\partial \boldsymbol{\mu}}{\partial \Re(s)} &= \mathbf{H}_{\text{eff}} \mathbf{w} \label{eq:mu_re_s}\\
\frac{\partial \boldsymbol{\mu}}{\partial \Im(s)} &= j \mathbf{H}_{\text{eff}} \mathbf{w} \label{eq:mu_im_s}
\end{align}

\subsection{Cross-Term Calculations}

\subsubsection{FIM$_{1,2}$: Coupling between $\theta_E$ and $\theta_L$}

\begin{align}
\text{FIM}_{1,2} &= \frac{2}{\sigma_z^2} \Re\left\{\left(\frac{\partial \boldsymbol{\mu}}{\partial \theta_E}\right)^H \frac{\partial \boldsymbol{\mu}}{\partial \theta_L}\right\} \nonumber \\
&= \frac{2}{\sigma_z^2} \Re\left\{|\beta s|^2 (\mathbf{w}^H \mathbf{a}) \left(\frac{\partial \mathbf{b}^H}{\partial \theta_E}\right) \mathbf{b}(\theta_E) \frac{\partial \mathbf{a}^H}{\partial \theta_L} \mathbf{w}\right\} \label{eq:fim_12}
\end{align}

\textbf{Key Observation}: This term is generally \textcolor{red}{\textbf{NON-ZERO}} because:
\begin{itemize}
    \item Both angles affect the same signal path through the target
    \item The coupling depends on the array geometry and beamforming vector
    \item Only zero in very specific geometric configurations
\end{itemize}

\subsubsection{FIM$_{1,3}$: Coupling between $\theta_E$ and $\Re(s)$}

\begin{align}
\text{FIM}_{1,3} &= \frac{2}{\sigma_z^2} \Re\left\{\left(\frac{\partial \boldsymbol{\mu}}{\partial \theta_E}\right)^H \frac{\partial \boldsymbol{\mu}}{\partial \Re(s)}\right\} \nonumber \\
&= \frac{2}{\sigma_z^2} \Re\left\{\beta s^* (\mathbf{a}^H \mathbf{w})^* \left(\frac{\partial \mathbf{b}^H}{\partial \theta_E}\right) \mathbf{H}_{\text{eff}} \mathbf{w}\right\} \label{eq:fim_13}
\end{align}

\textbf{Analysis}:
\begin{itemize}
    \item \textcolor{red}{\textbf{NON-ZERO}} in general
    \item Represents coupling between eavesdropper angle and communication symbol
    \item Magnitude depends on $\Re\{\beta s^*\}$ and channel correlation
\end{itemize}

\subsubsection{FIM$_{1,4}$: Coupling between $\theta_E$ and $\Im(s)$}

\begin{align}
\text{FIM}_{1,4} &= \frac{2}{\sigma_z^2} \Re\left\{\left(\frac{\partial \boldsymbol{\mu}}{\partial \theta_E}\right)^H \frac{\partial \boldsymbol{\mu}}{\partial \Im(s)}\right\} \nonumber \\
&= \frac{2}{\sigma_z^2} \Re\left\{j \beta s^* (\mathbf{a}^H \mathbf{w})^* \left(\frac{\partial \mathbf{b}^H}{\partial \theta_E}\right) \mathbf{H}_{\text{eff}} \mathbf{w}\right\} \nonumber \\
&= -\frac{2}{\sigma_z^2} \Im\left\{\beta s^* (\mathbf{a}^H \mathbf{w})^* \left(\frac{\partial \mathbf{b}^H}{\partial \theta_E}\right) \mathbf{H}_{\text{eff}} \mathbf{w}\right\} \label{eq:fim_14}
\end{align}

\subsubsection{FIM$_{2,3}$ and FIM$_{2,4}$: Similar analysis for $\theta_L$}

Following similar derivations:
\begin{align}
\text{FIM}_{2,3} &= \frac{2}{\sigma_z^2} \Re\left\{\beta s^* \left(\frac{\partial \mathbf{a}}{\partial \theta_L}\right)^T \mathbf{w}^* \mathbf{b}^H(\theta_E) \mathbf{H}_{\text{eff}} \mathbf{w}\right\} \\
\text{FIM}_{2,4} &= -\frac{2}{\sigma_z^2} \Im\left\{\beta s^* \left(\frac{\partial \mathbf{a}}{\partial \theta_L}\right)^T \mathbf{w}^* \mathbf{b}^H(\theta_E) \mathbf{H}_{\text{eff}} \mathbf{w}\right\}
\end{align}

\subsubsection{FIM$_{3,4}$: Coupling between $\Re(s)$ and $\Im(s)$}

\begin{align}
\text{FIM}_{3,4} &= \frac{2}{\sigma_z^2} \Re\left\{(\mathbf{H}_{\text{eff}} \mathbf{w})^H (j \mathbf{H}_{\text{eff}} \mathbf{w})\right\} \nonumber \\
&= \frac{2}{\sigma_z^2} \Re\left\{j \|\mathbf{H}_{\text{eff}} \mathbf{w}\|^2\right\} = 0 \label{eq:fim_34}
\end{align}

\textbf{Special Case}: This is the \textcolor{green}{\textbf{ONLY cross-term that is always ZERO}} because $\Re\{j \cdot \text{real number}\} = 0$.

\section{When Are Cross-Terms Zero?}

\subsection{General Conditions for Zero Cross-Terms}

Cross-terms are zero when:
\begin{equation}
\Re\left\{\left(\frac{\partial \boldsymbol{\mu}}{\partial \phi_i}\right)^H \frac{\partial \boldsymbol{\mu}}{\partial \phi_j}\right\} = 0
\end{equation}

\subsection{Specific Zero Conditions}

\subsubsection{Orthogonal Signal Paths}
If the sensing and communication signal paths are orthogonal:
\begin{equation}
\Re\left\{(\mathbf{G}_E \mathbf{w})^H (\mathbf{H}_{CE}^H \mathbf{w})\right\} = 0
\end{equation}

\subsubsection{Special Array Geometries}
For certain array configurations where:
\begin{equation}
\left(\frac{\partial \mathbf{b}^H}{\partial \theta_E}\right) \mathbf{b}(\theta_E) = 0 \quad \text{or} \quad \frac{\partial \mathbf{a}^H}{\partial \theta_L} \mathbf{w} = 0
\end{equation}

\subsubsection{Weak Coupling Approximation}
When the reflection coefficient is very small: $|\beta| \approx 0$, the sensing-communication cross-terms become negligible.

\section{Physical Interpretation of Cross-Terms}

\subsection{Parameter Coupling Effects}

\begin{table}[h]
\centering
\begin{tabular}{|c|c|c|}
\hline
\textbf{Cross-Term} & \textbf{Physical Meaning} & \textbf{Typical Value} \\
\hline
FIM$_{1,2}$ & Angle-angle coupling & Non-zero \\
FIM$_{1,3}$, FIM$_{1,4}$ & Eavesdropper angle-symbol coupling & Non-zero \\
FIM$_{2,3}$, FIM$_{2,4}$ & Target angle-symbol coupling & Non-zero \\
FIM$_{3,4}$ & Real-imaginary symbol coupling & Always zero \\
\hline
\end{tabular}
\caption{Cross-term physical interpretation}
\end{table}

\subsection{Impact on Estimation Performance}

\subsubsection{Strong Coupling ($|\text{FIM}_{i,j}| >> 0$)}
\begin{itemize}
    \item Parameters are highly correlated
    \item Estimating one parameter affects others significantly
    \item Higher overall estimation uncertainty
    \item CRB matrix has large off-diagonal elements
\end{itemize}

\subsubsection{Weak Coupling ($|\text{FIM}_{i,j}| \approx 0$)}
\begin{itemize}
    \item Parameters can be estimated independently
    \item FIM is approximately block-diagonal
    \item Individual CRBs are achievable
    \item Simpler estimation algorithms possible
\end{itemize}

\section{Numerical Example}

Consider a system with:
\begin{itemize}
    \item $N_{BS} = 16$, $N_E = 8$
    \item $\theta_E = 45°$, $\theta_L = 30°$
    \item $|\beta| = 0.8$, $|s| = 1$
    \item Uniform beamforming: $\mathbf{w} = \frac{1}{\sqrt{N_{BS}}} \mathbf{1}$
\end{itemize}

\subsection{Typical FIM Structure}

\begin{equation}
\mathbf{J} \approx \begin{bmatrix}
47.1 & 12.3 & 8.7 & -2.1 \\
12.3 & 52.8 & 15.2 & -4.3 \\
8.7 & 15.2 & 64.0 & 0 \\
-2.1 & -4.3 & 0 & 64.0
\end{bmatrix}
\end{equation}

\textbf{Observations}:
\begin{itemize}
    \item Most cross-terms are non-zero
    \item FIM$_{3,4} = 0$ (as expected)
    \item Significant coupling between angles and symbols
    \item Strong angle-angle coupling (FIM$_{1,2} = 12.3$)
\end{itemize}

\section{Implications for ISAC Security}

\subsection{Security Analysis Impact}

\subsubsection{Non-Zero Cross-Terms Mean}:
\begin{itemize}
    \item \textbf{Joint estimation is more effective} than separate estimation
    \item \textbf{Information leakage is correlated} between sensing and communication
    \item \textbf{Security metrics must consider coupling}
    \item \textbf{Countermeasures should address joint vulnerabilities}
\end{itemize}

\subsubsection{Design Implications}:
\begin{itemize}
    \item Cannot ignore parameter coupling in security analysis
    \item Joint CRB provides more accurate security assessment
    \item Need sophisticated countermeasures that consider coupling
    \item System optimization should account for cross-parameter effects
\end{itemize}

\section{MATLAB Implementation}

\begin{verbatim}
function [FIM, cross_terms] = compute_FIM_with_cross_terms(params)
    % Extract parameters
    theta_E = params.theta_E;
    theta_L = params.theta_L;
    s = params.s;
    beta = params.beta;
    w = params.w;
    H_CE = params.H_CE;
    sigma_z2 = params.sigma_z2;
    
    % Steering vectors and derivatives
    [a, da_dtheta] = steering_vector_and_derivative(theta_L, params.N_BS);
    [b, db_dtheta] = steering_vector_and_derivative(theta_E, params.N_E);
    
    % Effective channel
    G_E = beta * b * a';
    H_eff = G_E + H_CE';
    
    % Partial derivatives of mean vector
    dmu_dtheta_E = beta * s * (a' * w) * db_dtheta;
    dmu_dtheta_L = beta * s * (da_dtheta' * w) * b;
    dmu_dre_s = H_eff * w;
    dmu_dim_s = 1j * H_eff * w;
    
    % Compute FIM elements
    FIM = zeros(4, 4);
    
    % Diagonal terms
    FIM(1,1) = (2/sigma_z2) * real(dmu_dtheta_E' * dmu_dtheta_E);
    FIM(2,2) = (2/sigma_z2) * real(dmu_dtheta_L' * dmu_dtheta_L);
    FIM(3,3) = (2/sigma_z2) * real(dmu_dre_s' * dmu_dre_s);
    FIM(4,4) = (2/sigma_z2) * real(dmu_dim_s' * dmu_dim_s);
    
    % Cross terms
    FIM(1,2) = (2/sigma_z2) * real(dmu_dtheta_E' * dmu_dtheta_L);
    FIM(1,3) = (2/sigma_z2) * real(dmu_dtheta_E' * dmu_dre_s);
    FIM(1,4) = (2/sigma_z2) * real(dmu_dtheta_E' * dmu_dim_s);
    FIM(2,3) = (2/sigma_z2) * real(dmu_dtheta_L' * dmu_dre_s);
    FIM(2,4) = (2/sigma_z2) * real(dmu_dtheta_L' * dmu_dim_s);
    FIM(3,4) = (2/sigma_z2) * real(dmu_dre_s' * dmu_dim_s);
    
    % Symmetrize
    FIM = FIM + FIM' - diag(diag(FIM));
    
    % Extract cross terms
    cross_terms.theta_E_theta_L = FIM(1,2);
    cross_terms.theta_E_re_s = FIM(1,3);
    cross_terms.theta_E_im_s = FIM(1,4);
    cross_terms.theta_L_re_s = FIM(2,3);
    cross_terms.theta_L_im_s = FIM(2,4);
    cross_terms.re_s_im_s = FIM(3,4);
end
\end{verbatim}

\section{Conclusion}

\textbf{Key Findings}:
\begin{enumerate}
    \item \textcolor{red}{\textbf{Most cross-terms are NON-ZERO}} in ISAC systems
    \item \textcolor{green}{\textbf{Only FIM$_{3,4}$ (real-imaginary coupling) is always zero}}
    \item Cross-terms represent fundamental parameter coupling
    \item Ignoring cross-terms leads to inaccurate security analysis
    \item Joint estimation and countermeasures are essential
\end{enumerate}

\textbf{Practical Implications}:
\begin{itemize}
    \item Security analysis must consider parameter coupling
    \item Joint CRB provides more realistic bounds
    \item System design should account for cross-parameter effects
    \item Countermeasures should address joint vulnerabilities
\end{itemize}

\end{document}
