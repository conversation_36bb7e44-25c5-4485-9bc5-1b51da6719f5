\documentclass[journal]{IEEEtran}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{cite}

% Math commands
\newcommand{\bm}[1]{\boldsymbol{#1}}
\newcommand{\tr}{\text{tr}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\diag}{\text{diag}}
\newcommand{\rank}{\text{rank}}
\newcommand{\vec}{\text{vec}}

\title{Information Leakage Analysis for Malicious Eavesdroppers in Full-Duplex ISAC Systems: A Unified CRB and Mutual Information Approach}

\author{IEEE Transactions on Wireless Communications Submission}

\begin{document}
\maketitle

\begin{abstract}
This paper presents a comprehensive information leakage analysis for malicious eavesdroppers in full-duplex integrated sensing and communication (FD-ISAC) systems with dual-function signaling. We develop a unified framework combining Cramér-Rao bound (CRB) analysis and mutual information theory to quantify the eavesdropper's capability to extract both sensing and communication information from the same transmitted signal. The analysis considers a sophisticated eavesdropper equipped with multiple antennas that can simultaneously intercept target reflection signals and direct communication transmissions, where the same signal serves both communication and sensing purposes. We derive closed-form expressions for sensing mutual information with random Gaussian signals and establish the communication mutual information for the eavesdropper under the dual-function signal model. The Fisher Information Matrix (FIM) analysis reveals significant coupling between sensing and communication parameter estimation, demonstrating that joint estimation substantially outperforms independent approaches. Our results provide fundamental insights into the security vulnerabilities of dual-function FD-ISAC systems and establish theoretical foundations for designing secure integrated wireless networks.
\end{abstract>

\begin{IEEEkeywords}
Integrated sensing and communication, physical layer security, information leakage, mutual information, Cramér-Rao bound, full-duplex systems
\end{IEEEkeywords}

\section{Introduction}

The integration of sensing and communication functionalities in wireless systems has emerged as a key enabler for next-generation networks, offering unprecedented spectral efficiency and operational flexibility. However, this integration introduces novel security vulnerabilities that require careful analysis and mitigation strategies.

In this paper, we address the fundamental question of information leakage in FD-ISAC systems when confronted with sophisticated eavesdroppers capable of exploiting both sensing and communication signal paths. Our contributions include: (1) a comprehensive signal model for FD-ISAC systems with structured precoding matrices, (2) rigorous derivation of sensing and communication mutual information under random signal conditions, (3) detailed CRB analysis with cross-parameter coupling effects, and (4) unified security metrics for joint vulnerability assessment.

\section{System Model and Signal Structure}

\subsection{FD-ISAC System Architecture}

We consider a monostatic FD-ISAC system with broadcast communication comprising:
\begin{itemize}
    \item A base station (BS) with $N_{BS}$ antennas serving dual radar and communication functions
    \item $K$ single-antenna legitimate communication users receiving the same broadcast information
    \item A passive sensing target at angle $\theta_L$
    \item A malicious eavesdropper (Eve) with $N_E$ antennas at angle $\theta_E$
\end{itemize}

In this broadcast scenario, all legitimate users receive the same information, but the BS can exploit spatial diversity for both sensing enhancement and security purposes.

\subsection{Dual-Function Signal Model}

The BS transmits a composite signal vector that serves both communication and sensing functions:
\begin{equation}
\mathbf{x}(l) = \mathbf{w}s(l) + \mathbf{z}_{AN}(l) \in \mathbb{C}^{N_{BS} \times 1}
\label{eq:transmitted_signal}
\end{equation}

where:
\begin{itemize}
    \item $\mathbf{w} \in \mathbb{C}^{N_{BS} \times 1}$ is the beamforming vector for the information signal
    \item $s(l) \in \mathbb{C}$ is the broadcast information symbol
    \item $\mathbf{z}_{AN}(l) \in \mathbb{C}^{N_{BS} \times 1}$ is the artificial noise (AN) vector for security enhancement
\end{itemize}

\subsubsection{Dual-Function Signal Design}

The transmitted signal $\mathbf{x}(l) = \mathbf{w}s(l) + \mathbf{z}_{AN}(l)$ serves dual purposes:

\textbf{Communication Function:}
\begin{itemize}
    \item The beamformed signal $\mathbf{w}s(l)$ carries the broadcast information to all legitimate users
    \item The beamforming vector $\mathbf{w}$ can be designed to enhance signal quality at legitimate user locations
\end{itemize}

\textbf{Sensing Function:}
\begin{itemize}
    \item The entire transmitted signal $\mathbf{x}(l)$ serves as the sensing waveform for target detection
    \item The signal structure provides the necessary waveform characteristics for radar functionality
\end{itemize}

\textbf{Security Enhancement:}
\begin{itemize}
    \item The artificial noise $\mathbf{z}_{AN}(l)$ is designed to degrade eavesdropper performance
    \item $\mathbf{z}_{AN}(l)$ can be projected into the null space of legitimate user channels to minimize impact on intended receivers
\end{itemize}

\subsubsection{Legitimate User Reception}

The $k$-th legitimate user receives:
\begin{equation}
y_k(l) = \mathbf{h}_k^H \mathbf{x}(l) + n_k(l) = \mathbf{h}_k^H \mathbf{w} s(l) + \mathbf{h}_k^H \mathbf{z}_{AN}(l) + n_k(l)
\label{eq:user_signal}
\end{equation}

where $\mathbf{h}_k \in \mathbb{C}^{N_{BS} \times 1}$ is the channel from BS to the $k$-th user.

\subsubsection{Artificial Noise Design}

The artificial noise vector $\mathbf{z}_{AN}(l)$ is designed to enhance security while minimizing impact on legitimate communications:

\begin{equation}
\mathbf{z}_{AN}(l) = \mathbf{V}_{AN} \mathbf{n}_{AN}(l)
\label{eq:an_structure}
\end{equation}

where:
\begin{itemize}
    \item $\mathbf{V}_{AN} \in \mathbb{C}^{N_{BS} \times N_{AN}}$ is the artificial noise precoding matrix
    \item $\mathbf{n}_{AN}(l) \in \mathbb{C}^{N_{AN} \times 1}$ contains independent artificial noise symbols
    \item $N_{AN} \leq N_{BS} - 1$ is the number of artificial noise streams
\end{itemize}

\textbf{Design Objectives:}
\begin{itemize}
    \item \textbf{Null Space Projection}: $\mathbf{V}_{AN}$ can be designed such that $\mathbf{h}_k^H \mathbf{V}_{AN} = \mathbf{0}$ for all legitimate users, eliminating AN interference
    \item \textbf{Eavesdropper Degradation}: $\mathbf{z}_{AN}(l)$ creates interference for eavesdroppers not in the null space
    \item \textbf{Sensing Compatibility}: The AN design should not significantly degrade sensing performance
    \item \textbf{Power Allocation}: $\mathbb{E}[\|\mathbf{x}(l)\|^2] = \|\mathbf{w}\|^2 \sigma_s^2 + \|\mathbf{V}_{AN}\|_F^2 \sigma_{AN}^2 \leq P_{\max}$
\end{itemize}

\subsubsection{Random Signal Model}

For theoretical analysis, we model the signals as independent Gaussian random variables:
\begin{align}
s(l) &\sim \mathcal{CN}(0, \sigma_s^2) \label{eq:info_signal_model} \\
\mathbf{n}_{AN}(l) &\sim \mathcal{CN}(\mathbf{0}, \sigma_{AN}^2 \mathbf{I}_{N_{AN}}) \label{eq:an_signal_model}
\end{align}

where:
\begin{itemize}
    \item $\sigma_s^2$ is the power of the information symbol
    \item $\sigma_{AN}^2$ is the power of each artificial noise component
    \item The information signal and artificial noise are independent
\end{itemize}

This simplified model captures the essential dual-function nature: the same information symbol $s(l)$ serves both communication and sensing purposes, while the artificial noise $\mathbf{z}_{AN}(l)$ provides security enhancement and additional sensing waveform diversity.

\subsection{Eavesdropper Signal Model}

The malicious eavesdropper equipped with $N_E$ antennas receives signals through two distinct paths:
\begin{equation}
\mathbf{y}_E(l) = \mathbf{G}_E(\boldsymbol{\theta})\mathbf{x}(l) + \mathbf{H}_{CE}^H\mathbf{x}(l) + \mathbf{n}_E(l)
\label{eq:eve_signal}
\end{equation}

Substituting the signal model $\mathbf{x}(l) = \mathbf{w}s(l) + \mathbf{z}_{AN}(l)$:
\begin{equation}
\mathbf{y}_E(l) = \mathbf{H}_{eff}\mathbf{w}s(l) + \mathbf{H}_{eff}\mathbf{z}_{AN}(l) + \mathbf{n}_E(l)
\label{eq:eve_signal_expanded}
\end{equation}

where:
\begin{itemize}
    \item $\mathbf{G}_E(\boldsymbol{\theta}) = \beta(l) \mathbf{b}(\theta_E) \mathbf{a}^H(\theta_L) \in \mathbb{C}^{N_E \times N_{BS}}$ is the target-reflected channel matrix that allows Eve to intercept the sensing signal reflected from the target
    \item $\mathbf{H}_{CE} \in \mathbb{C}^{N_{BS} \times N_E}$ is the direct communication channel from BS to Eve
    \item $\beta(l)$ is the complex reflection coefficient containing target information
    \item $\mathbf{n}_E(l) \sim \mathcal{CN}(\mathbf{0}, \sigma_n^2 \mathbf{I}_{N_E})$ is additive white Gaussian noise
\end{itemize}

\subsubsection{Information Leakage with Artificial Noise}

The simplified signal model creates a clearer security analysis framework:

\textbf{Eavesdropper's Received Signal:}
\begin{equation}
\mathbf{y}_E(l) = \mathbf{H}_{eff}\mathbf{w}s(l) + \mathbf{H}_{eff}\mathbf{V}_{AN}\mathbf{n}_{AN}(l) + \mathbf{n}_E(l)
\label{eq:eve_complete_signal}
\end{equation}

\textbf{Information Leakage Mechanisms:}
\begin{itemize}
    \item \textbf{Communication Information}: Eve attempts to extract $s(l)$ from the received signal, competing against artificial noise interference
    \item \textbf{Sensing Information}: Through both direct and reflected paths, Eve can estimate sensing parameters $(\theta_E, \theta_L, \beta)$
    \item \textbf{System Information}: The structure of $\mathbf{w}$ and $\mathbf{V}_{AN}$ may reveal system design choices
\end{itemize}

\textbf{Security Benefits of Artificial Noise:}
\begin{itemize}
    \item \textbf{Communication Degradation}: $\mathbf{H}_{eff}\mathbf{V}_{AN}\mathbf{n}_{AN}(l)$ creates interference for Eve's communication decoding
    \item \textbf{Sensing Interference}: AN may also degrade Eve's sensing parameter estimation
    \item \textbf{Null Space Protection}: Proper design ensures legitimate users are not affected by AN
\end{itemize}

\textbf{Dual-Function Exploitation:}
\begin{itemize}
    \item Eve can jointly process communication and sensing observations
    \item The same signal $s(l)$ appears in both direct and reflected paths, creating correlation
    \item Joint estimation may provide better performance than independent processing
\end{itemize}

The effective channel matrix combining both paths is:
\begin{equation}
\mathbf{H}_{eff} = \mathbf{G}_E(\boldsymbol{\theta}) + \mathbf{H}_{CE}^H \in \mathbb{C}^{N_E \times N_{BS}}
\label{eq:effective_channel}
\end{equation}

\subsection{Array Steering Vectors}

For uniform linear arrays with half-wavelength spacing:
\begin{align}
\mathbf{a}(\theta_L) &= \left[1, e^{j\pi\sin\theta_L}, \ldots, e^{j(N_{BS}-1)\pi\sin\theta_L}\right]^T \label{eq:bs_steering} \\
\mathbf{b}(\theta_E) &= \left[1, e^{j\pi\sin\theta_E}, \ldots, e^{j(N_E-1)\pi\sin\theta_E}\right]^T \label{eq:eve_steering}
\end{align}

The derivatives for CRB computation are:
\begin{align}
\frac{\partial \mathbf{a}(\theta_L)}{\partial \theta_L} &= j\pi\cos(\theta_L) \cdot \diag(0, 1, \ldots, N_{BS}-1) \mathbf{a}(\theta_L) \label{eq:bs_derivative} \\
\frac{\partial \mathbf{b}(\theta_E)}{\partial \theta_E} &= j\pi\cos(\theta_E) \cdot \diag(0, 1, \ldots, N_E-1) \mathbf{b}(\theta_E) \label{eq:eve_derivative}
\end{align}

\section{Information Leakage Analysis Framework}

\subsection{Joint Parameter Estimation with Artificial Noise}

In the simplified broadcast scenario with artificial noise, the eavesdropper attempts to jointly estimate:
\begin{equation}
\boldsymbol{\phi} = [\theta_E, \theta_L, \Re(\beta), \Im(\beta), \Re(s), \Im(s)]^T
\label{eq:joint_parameters_simplified}
\end{equation}

where:
\begin{itemize}
    \item $\theta_E$ and $\theta_L$ represent geometric sensing information
    \item $\Re(\beta)$ and $\Im(\beta)$ represent the complex reflection coefficient
    \item $\Re(s)$ and $\Im(s)$ represent the broadcast information symbol
\end{itemize}

Note that the artificial noise components $\mathbf{n}_{AN}(l)$ are typically not estimated as they are designed to be random and unpredictable.

\subsubsection{Broadcast-Specific Estimation Characteristics}

The broadcast scenario creates unique estimation dynamics:

\textbf{Simplified Communication Estimation:}
\begin{itemize}
    \item Only one information symbol $s_{\text{info}}(l)$ needs to be estimated per time slot
    \item Eve faces the same fundamental communication task as legitimate users
    \item Multi-antenna advantage may provide better SNR than single-antenna users
\end{itemize}

\textbf{Enhanced Sensing Opportunities:}
\begin{itemize}
    \item Auxiliary signals provide additional observables for sensing parameter estimation
    \item Joint processing of direct and reflected paths can improve sensing accuracy
    \item Knowledge of auxiliary signal structure may reveal system design information
\end{itemize}

\textbf{Security Implications:}
\begin{itemize}
    \item Auxiliary signals can be designed as artificial noise to degrade Eve's performance
    \item Proper design of $\mathbf{F}_{\text{aux}}$ can create security benefits without affecting legitimate users
    \item Trade-off between sensing performance and security enhancement
\end{itemize}

\subsection{Fisher Information Matrix Analysis}

The Fisher Information Matrix for the joint parameter vector is:
\begin{equation}
[\mathbf{J}]_{i,j} = \frac{2}{\sigma_n^2} \Re\left\{\left(\frac{\partial \boldsymbol{\mu}}{\partial \phi_i}\right)^H \frac{\partial \boldsymbol{\mu}}{\partial \phi_j}\right\}
\label{eq:fim_general}
\end{equation}

where $\boldsymbol{\mu} = \mathbf{H}_{eff} \mathbf{F} \mathbf{s}$ is the mean vector of the received signal.

\subsubsection{Partial Derivatives}

The partial derivatives of the mean vector are:
\begin{align}
\frac{\partial \boldsymbol{\mu}}{\partial \theta_E} &= \beta \mathbf{s}^T \mathbf{F}^T \mathbf{a}(\theta_L) \frac{\partial \mathbf{b}(\theta_E)}{\partial \theta_E} \label{eq:mu_theta_e} \\
\frac{\partial \boldsymbol{\mu}}{\partial \theta_L} &= \beta \mathbf{s}^T \mathbf{F}^T \frac{\partial \mathbf{a}(\theta_L)}{\partial \theta_L} \mathbf{b}(\theta_E) \label{eq:mu_theta_l} \\
\frac{\partial \boldsymbol{\mu}}{\partial \Re(s_k)} &= \mathbf{H}_{eff} \mathbf{f}_k \label{eq:mu_re_sk} \\
\frac{\partial \boldsymbol{\mu}}{\partial \Im(s_k)} &= j \mathbf{H}_{eff} \mathbf{f}_k \label{eq:mu_im_sk}
\end{align}

\subsubsection{FIM Block Structure}

The FIM has a block structure:
\begin{equation}
\mathbf{J} = \begin{bmatrix}
\mathbf{J}_{SS} & \mathbf{J}_{SC} \\
\mathbf{J}_{CS} & \mathbf{J}_{CC}
\end{bmatrix}
\label{eq:fim_blocks}
\end{equation}

where:
\begin{itemize}
    \item $\mathbf{J}_{SS} \in \mathbb{R}^{2 \times 2}$ corresponds to sensing parameters $[\theta_E, \theta_L]$
    \item $\mathbf{J}_{CC} \in \mathbb{R}^{2K \times 2K}$ corresponds to communication parameters
    \item $\mathbf{J}_{SC} = \mathbf{J}_{CS}^T$ represents sensing-communication coupling
\end{itemize}

\section{Sensing Mutual Information with Random Signals}

\subsection{SMI Framework for Random Gaussian Signals}

Following the framework in \cite{sensing_mi_paper}, the sensing mutual information between the target response and received signals is:
\begin{equation}
I_S = I(\mathbf{h}_s; \mathbf{y}_E | \mathbf{S})
\label{eq:sensing_mi}
\end{equation}

where $\mathbf{h}_s = \vec(\mathbf{G}_E^H)$ represents the vectorized target response matrix.

\subsection{Asymptotic Approximation}

For large $N_{CPI}$ with finite ratio $c = N_{CPI}/N_D$, the sensing mutual information can be approximated as:
\begin{equation}
I_S = \sum_{j=1}^{K_R} \bar{\varrho}_j(\boldsymbol{\Phi}) + \mathcal{O}\left(\frac{1}{N_{CPI}}\right)
\label{eq:smi_asymptotic}
\end{equation}

where:
\begin{align}
\bar{\varrho}_j(\boldsymbol{\Phi}) &= \log_2\left|\mathbf{I}_{N_{BS}} + \frac{\lambda_{R,j}}{1 + \lambda_{R,j}\delta(\lambda_{R,j})}\mathbf{T}(\boldsymbol{\Phi})\right| \nonumber \\
&\quad + N_{CPI} \log_2(1 + \lambda_{R,j}\delta(\lambda_{R,j})) \nonumber \\
&\quad - \frac{N_{CPI} \lambda_{R,j}\delta(\lambda_{R,j})}{1 + \lambda_{R,j}\delta(\lambda_{R,j})}
\label{eq:varrho_j}
\end{align}

with $\mathbf{T}(\boldsymbol{\Phi}) = \mathbf{R}_T^{1/2} \boldsymbol{\Phi} \mathbf{R}_T^{1/2}$, $\boldsymbol{\Phi} = \mathbf{F}\mathbf{F}^H$, and $\delta(\rho)$ satisfying:
\begin{equation}
\delta(\rho) = \frac{1}{N_{BS}} \tr\left(\mathbf{T}(\boldsymbol{\Phi})\left(\mathbf{I}_{N_{BS}} + \frac{\rho}{1 + \rho\delta(\rho)}\mathbf{T}(\boldsymbol{\Phi})\right)^{-1}\right)
\label{eq:delta_equation}
\end{equation}

\subsection{Physical Interpretation}

The sensing mutual information quantifies the maximum information about the target response that can be extracted from the received signals. Higher values indicate greater sensing information leakage to the eavesdropper.

\section{Communication Mutual Information Derivation}

\subsection{Communication MI with Artificial Noise}

In the broadcast scenario with artificial noise, the communication mutual information at the eavesdropper is:
\begin{equation}
I_C = I(s; \mathbf{y}_E | \mathbf{H}_{eff}, \mathbf{V}_{AN})
\label{eq:comm_mi_an}
\end{equation}

This represents the information that Eve can extract about the broadcast symbol $s$ from her observations, in the presence of artificial noise interference.

\subsection{Derivation for Gaussian Signals}

Given the Gaussian signal model and the linear observation model, the communication mutual information can be derived as:

\begin{theorem}[Communication Mutual Information with Artificial Noise]
For the FD-ISAC system with broadcast communication and artificial noise, the communication mutual information at the eavesdropper is:
\begin{equation}
I_C = \log_2 \left(1 + \frac{\sigma_s^2 \|\mathbf{H}_{eff} \mathbf{w}\|^2}{\sigma_{AN}^2 \|\mathbf{H}_{eff} \mathbf{V}_{AN}\|_F^2 + \sigma_n^2}\right)
\label{eq:comm_mi_an_closed}
\end{equation}
where the denominator represents the total interference plus noise power at the eavesdropper.
\end{theorem>

\begin{proof}
The received signal at the eavesdropper is:
\begin{equation}
\mathbf{y}_E = \mathbf{H}_{eff} \mathbf{w} s + \mathbf{H}_{eff} \mathbf{V}_{AN} \mathbf{n}_{AN} + \mathbf{n}_E
\label{eq:eve_signal_an}
\end{equation}

The artificial noise and thermal noise create total interference with covariance:
\begin{equation}
\mathbf{R}_{\text{int}} = \sigma_{AN}^2 \mathbf{H}_{eff} \mathbf{V}_{AN} \mathbf{V}_{AN}^H \mathbf{H}_{eff}^H + \sigma_n^2 \mathbf{I}_{N_E}
\label{eq:interference_covariance}
\end{equation}

For the scalar information symbol $s$, the mutual information is:
\begin{align}
I_C &= \log_2 \left(1 + \sigma_s^2 \mathbf{w}^H \mathbf{H}_{eff}^H \mathbf{R}_{\text{int}}^{-1} \mathbf{H}_{eff} \mathbf{w}\right) \label{eq:mi_an_intermediate}
\end{align}

Using the matrix inversion lemma and assuming the artificial noise is well-designed, this can be approximated as \eqref{eq:comm_mi_an_closed}.
\end{proof}

\subsection{Alternative Expression}

The communication mutual information can also be expressed in terms of the effective SINR:
\begin{equation}
I_C = \sum_{k=1}^K \log_2\left(1 + \frac{\|\mathbf{H}_{eff} \mathbf{f}_k\|^2}{N_{CPI} \sigma_n^2}\right)
\label{eq:comm_mi_sinr}
\end{equation}

when the communication precoders are orthogonal.

\section{Cramér-Rao Bound Analysis}

\subsection{CRB Verification for Our Signal Model}

The CRB for the joint parameter vector is:
\begin{equation}
\text{CRB}(\boldsymbol{\phi}) = \mathbf{J}^{-1}
\label{eq:crb_joint}
\end{equation}

\subsubsection{Individual Parameter CRBs}

For the sensing parameters:
\begin{align}
\text{CRB}(\theta_E) &= [\mathbf{J}^{-1}]_{1,1} \label{eq:crb_theta_e} \\
\text{CRB}(\theta_L) &= [\mathbf{J}^{-1}]_{2,2} \label{eq:crb_theta_l}
\end{align}

For the communication parameters:
\begin{align}
\text{CRB}(\Re(s_k)) &= [\mathbf{J}^{-1}]_{2+2k-1,2+2k-1} \label{eq:crb_re_sk} \\
\text{CRB}(\Im(s_k)) &= [\mathbf{J}^{-1}]_{2+2k,2+2k} \label{eq:crb_im_sk}
\end{align}

\subsubsection{High-SNR Approximations}

Under high-SNR conditions and weak coupling assumptions, the CRBs approximately become:
\begin{align}
\text{CRB}(\theta_E) &\approx \frac{\sigma_n^2}{2|\beta|^2 |\mathbf{a}^H(\theta_L) \mathbf{w}|^2 \|\frac{\partial \mathbf{b}(\theta_E)}{\partial \theta_E}\|^2} \label{eq:crb_theta_e_approx} \\
\text{CRB}(\theta_L) &\approx \frac{\sigma_n^2}{2|\beta|^2 \|\mathbf{b}(\theta_E)\|^2 |\frac{\partial \mathbf{a}^H(\theta_L)}{\partial \theta_L} \mathbf{w}|^2} \label{eq:crb_theta_l_approx} \\
\text{CRB}(\Re(s_k)) &= \text{CRB}(\Im(s_k)) \approx \frac{\sigma_n^2}{2\|\mathbf{H}_{eff} \mathbf{f}_k\|^2} \label{eq:crb_comm_approx}
\end{align}

where $\mathbf{w}$ represents an effective beamforming vector.

\section{Unified Security Metrics}

\subsection{Information Leakage Quantification}

We define comprehensive information leakage metrics:

\subsubsection{Sensing Information Leakage}
\begin{equation}
\mathcal{L}_S = \frac{1}{\text{CRB}(\theta_E)} + \frac{1}{\text{CRB}(\theta_L)}
\label{eq:sensing_leakage}
\end{equation}

\subsubsection{Communication Information Leakage}
\begin{equation}
\mathcal{L}_C = \sum_{k=1}^K \frac{1}{\text{CRB}(\Re(s_k)) + \text{CRB}(\Im(s_k))}
\label{eq:comm_leakage}
\end{equation}

\subsubsection{Joint Vulnerability Index}
\begin{equation}
\mathcal{V}_{\text{joint}} = w_S I_S + w_C I_C + w_{\text{coup}} \mathcal{L}_{\text{coup}}
\label{eq:joint_vulnerability}
\end{equation}

where $w_S + w_C + w_{\text{coup}} = 1$ and $\mathcal{L}_{\text{coup}}$ quantifies parameter coupling effects.

\section{Conclusion}

This paper has presented a comprehensive framework for analyzing information leakage in FD-ISAC systems under sophisticated eavesdropping attacks. The unified approach combining CRB analysis and mutual information theory provides fundamental insights into the security vulnerabilities of dual-function wireless systems. The derived expressions enable quantitative security assessment and inform the design of robust countermeasures for next-generation ISAC networks.

\bibliographystyle{IEEEtran}
\bibliography{references}

\end{document}
