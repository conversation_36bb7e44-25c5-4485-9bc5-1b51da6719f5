function fig_to_png(fig_filename, varargin)
% FIG_TO_PNG - Convert FIG file to PNG format
% Usage: fig_to_png('filename.fig', 'resolution', 300)

p = inputParser;
addRequired(p, 'fig_filename', @ischar);
addParameter(p, 'resolution', 300, @isnumeric);
parse(p, fig_filename, varargin{:});

if ~exist(p.Results.fig_filename, 'file')
    error('FIG file not found: %s', p.Results.fig_filename);
end

fig_handle = openfig(p.Results.fig_filename, 'invisible');
[~, name, ~] = fileparts(p.Results.fig_filename);
png_filename = [name '.png'];

print(fig_handle, png_filename, '-dpng', sprintf('-r%d', p.Results.resolution));
close(fig_handle);

fprintf('Converted %s to %s\n', p.Results.fig_filename, png_filename);
end
