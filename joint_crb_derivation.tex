\documentclass{article}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}

% Math commands
\newcommand{\bm}[1]{\boldsymbol{#1}}
\newcommand{\tr}{\text{tr}}
\newcommand{\diag}{\text{diag}}
\newcommand{\rank}{\text{rank}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}

\title{Joint Cramér-Rao Bound Derivation for Secure ISAC Systems}
\author{Based on GUIDE Prompt for Union CRB Derivation}
\date{\today}

\begin{document}
\maketitle

\section{Introduction}

In this document, we derive the joint Cramér-<PERSON> Bound (CRB) for sensing and communication parameters in a secure Integrated Sensing and Communication (ISAC) system. The eavesdropper (Eve) receives a superimposed signal containing both sensing reflection and direct communication components. We treat the sensing parameters ($\theta_E$, $\theta_L$) and communication parameters (real and imaginary parts of the symbol $s$) as a joint parameter vector $\boldsymbol{\phi} = [\theta_E, \theta_L, \Re(s), \Im(s)]^T$.

The CRB provides a lower bound on the mean squared error (MSE) of unbiased estimators, serving as a security metric: a higher CRB for Eve indicates better system security against parameter estimation (e.g., target location from angles or communication symbol decoding).

\section{System Model and Assumptions}

Eve's received signal is given by:
\begin{equation}
\mathbf{y} = (\mathbf{G}_E + \mathbf{H}_E) \mathbf{w} s + \mathbf{z}
\end{equation}

where:
\begin{itemize}
    \item $\mathbf{y} \in \mathbb{C}^{N_E \times 1}$ is the observation at Eve with $N_E$ antennas
    \item $\mathbf{G}_E = \alpha_E \mathbf{b}(\theta_E) \mathbf{a}^H(\theta_L)$ is the sensing reflection channel ($N_E \times N_{BS}$), with $\alpha_E$ a complex coefficient, $\mathbf{b}(\theta_E) \in \mathbb{C}^{N_E \times 1}$ Eve's receive steering vector, and $\mathbf{a}(\theta_L) \in \mathbb{C}^{N_{BS} \times 1}$ the BS transmit steering vector ($N_{BS}$ BS antennas)
    \item $\mathbf{H}_E \in \mathbb{C}^{N_E \times N_{BS}}$ is the direct communication channel
    \item $\mathbf{w} \in \mathbb{C}^{N_{BS} \times 1}$ is the precoding vector
    \item $s \in \mathbb{C}$ is the communication symbol
    \item $\mathbf{z} \sim \mathcal{CN}(0, \sigma_z^2 \mathbf{I})$ is complex Gaussian noise
\end{itemize}

Define the combined channel $\mathbf{A} = \mathbf{G}_E + \mathbf{H}_E$, so:
\begin{equation}
\boldsymbol{\mu}(\boldsymbol{\phi}) = \mathbf{A} \mathbf{w} s
\end{equation}

and $\mathbf{y} \sim \mathcal{CN}(\boldsymbol{\mu}(\boldsymbol{\phi}), \sigma_z^2 \mathbf{I})$.

\subsection{Steering Vector Model}

Assume uniform linear arrays (ULA) for steering vectors, e.g.:
\begin{equation}
\mathbf{b}(\theta_E) = \begin{bmatrix} 1 \\ e^{j \pi \sin(\theta_E)} \\ \vdots \\ e^{j (N_E-1) \pi \sin(\theta_E)} \end{bmatrix}
\end{equation}

with derivative:
\begin{equation}
\frac{\partial \mathbf{b}}{\partial \theta_E} = j \pi \cos(\theta_E) \cdot \text{diag}(0, 1, \ldots, N_E-1) \mathbf{b}(\theta_E)
\end{equation}

Similarly for $\mathbf{a}(\theta_L)$.

\section{Derivation of the Joint CRB}

\subsection{Joint Parameter Vector}

Following the GUIDE approach, we define the joint parameter vector that the eavesdropper attempts to estimate:
\begin{equation}
\boldsymbol{\phi} = \begin{bmatrix} \theta_E \\ \theta_L \\ \Re(s) \\ \Im(s) \end{bmatrix}
\end{equation}

where:
\begin{itemize}
    \item $\theta_E$ is the eavesdropper's angle (Eve's own location)
    \item $\theta_L$ is the target angle (sensing information)
    \item $\Re(s)$ and $\Im(s)$ are the real and imaginary parts of the communication symbol
\end{itemize}

\textbf{Important Note}: This formulation considers the eavesdropper's perspective, where Eve attempts to estimate both sensing parameters ($\theta_E$, $\theta_L$) and communication parameters ($\Re(s)$, $\Im(s)$).

\subsection{Fisher Information Matrix Computation}

The CRB is the inverse of the Fisher Information Matrix (FIM):
\begin{equation}
\text{CRB} = \text{FIM}^{-1}
\end{equation}

where FIM is a $4 \times 4$ matrix. Using the Slepian-Bangs formula for complex Gaussian observations:
\begin{equation}
\text{FIM}_{i,j} = \frac{2}{\sigma_z^2} \Re \left( \left( \frac{\partial \boldsymbol{\mu}}{\partial \phi_i} \right)^H \frac{\partial \boldsymbol{\mu}}{\partial \phi_j} \right)
\end{equation}

\subsubsection{Step 1: Compute Partial Derivatives}

The partial derivatives of $\boldsymbol{\mu}$ with respect to each parameter in $\boldsymbol{\phi}$ are:
\begin{align}
    \frac{\partial \boldsymbol{\mu}}{\partial \theta_E} &= \alpha_E s (\mathbf{a}^H \mathbf{w}) \frac{\partial \mathbf{b}(\theta_E)}{\partial \theta_E} \\
    \frac{\partial \boldsymbol{\mu}}{\partial \theta_L} &= \alpha_E s \left( \frac{\partial \mathbf{a}^H(\theta_L)}{\partial \theta_L} \mathbf{w} \right) \mathbf{b}(\theta_E) \\
    \frac{\partial \boldsymbol{\mu}}{\partial \Re(s)} &= \mathbf{A} \mathbf{w} \\
    \frac{\partial \boldsymbol{\mu}}{\partial \Im(s)} &= j \mathbf{A} \mathbf{w}
\end{align}

\subsubsection{Step 2: Compute FIM Elements}

Label the parameters as $\phi_1 = \theta_E$, $\phi_2 = \theta_L$, $\phi_3 = \Re(s)$, $\phi_4 = \Im(s)$.

For $\text{FIM}_{1,1}$ (self-information for $\theta_E$):
\begin{equation}
\text{FIM}_{1,1} = \frac{2}{\sigma_z^2} \Re \left[ |\alpha_E s (\mathbf{a}^H \mathbf{w})|^2 \left\| \frac{\partial \mathbf{b}}{\partial \theta_E} \right\|^2 \right] = \frac{2 |\alpha_E s (\mathbf{a}^H \mathbf{w})|^2}{\sigma_z^2} \left\| \frac{\partial \mathbf{b}}{\partial \theta_E} \right\|^2
\end{equation}

For $\text{FIM}_{2,2}$ (for $\theta_L$):
\begin{equation}
\text{FIM}_{2,2} = \frac{2 |\alpha_E s|^2}{\sigma_z^2} \mathbf{b}^H(\theta_E) \mathbf{b}(\theta_E) \left\| \frac{\partial \mathbf{a}^H}{\partial \theta_L} \mathbf{w} \right\|^2 = \frac{2 |\alpha_E s|^2 N_E}{\sigma_z^2} \left\| \frac{\partial \mathbf{a}^H}{\partial \theta_L} \mathbf{w} \right\|^2
\end{equation}

since $\mathbf{b}^H \mathbf{b} = N_E$.

For $\text{FIM}_{3,3}$ (for $\Re(s)$):
\begin{equation}
\text{FIM}_{3,3} = \frac{2}{\sigma_z^2} \Re \left[ (\mathbf{A} \mathbf{w})^H (\mathbf{A} \mathbf{w}) \right] = \frac{2}{\sigma_z^2} \| \mathbf{A} \mathbf{w} \|^2
\end{equation}

For $\text{FIM}_{4,4}$ (for $\Im(s)$):
\begin{equation}
\text{FIM}_{4,4} = \frac{2}{\sigma_z^2} \Re \left[ (j \mathbf{A} \mathbf{w})^H (j \mathbf{A} \mathbf{w}) \right] = \frac{2}{\sigma_z^2} \| \mathbf{A} \mathbf{w} \|^2
\end{equation}

since $j^H j = (-j) j = -j^2 = 1$.

For cross-terms, e.g., $\text{FIM}_{1,3}$ (coupling $\theta_E$ and $\Re(s)$):
\begin{equation}
\text{FIM}_{1,3} = \frac{2}{\sigma_z^2} \Re \left[ \left( \alpha_E s^* (\mathbf{w}^H \mathbf{a}) \frac{\partial \mathbf{b}^H}{\partial \theta_E} \right) (\mathbf{A} \mathbf{w}) \right]
\end{equation}

where $(\cdot)^*$ is complex conjugate. Off-diagonal elements capture coupling: non-zero values indicate that estimating one parameter affects the precision of another (e.g., sensing angles interfering with symbol estimation).

\subsubsection{Step 3: Invert FIM for CRB}

The full FIM is:
\begin{equation}
\text{FIM} = \frac{2}{\sigma_z^2} \begin{bmatrix}
f_{11} & f_{12} & f_{13} & f_{14} \\
f_{21} & f_{22} & f_{23} & f_{24} \\
f_{31} & f_{32} & f_{33} & f_{34} \\
f_{41} & f_{42} & f_{43} & f_{44}
\end{bmatrix}
\end{equation}

where $f_{ij} = \Re \left( \left( \frac{\partial \boldsymbol{\mu}}{\partial \phi_i} \right)^H \frac{\partial \boldsymbol{\mu}}{\partial \phi_j} \right)$. Invert symbolically or numerically to get CRB.

The MSE lower bound for communication symbol $s$ is CRB$_{3,3}$ + CRB$_{4,4}$ (total variance for real/imag parts). For sensing, CRB$_{1,1}$ + CRB$_{2,2}$ bounds angle estimation errors.

\textbf{Physical Interpretation}: The off-diagonal terms capture coupling between different parameter estimations. Strong coupling indicates that estimating one parameter significantly affects the precision of another, highlighting the fundamental trade-offs in ISAC systems.

\section{Security Metrics Based on Joint CRB}

\subsection{Joint CRB Matrix}

The joint CRB matrix is:
\begin{equation}
\mathbf{C}_{\text{joint}} = \text{FIM}^{-1} = \begin{bmatrix}
\text{CRB}_{\theta_E} & \text{CRB}_{\theta_E,\theta_L} & \text{CRB}_{\theta_E,\Re(s)} & \text{CRB}_{\theta_E,\Im(s)} \\
\text{CRB}_{\theta_L,\theta_E} & \text{CRB}_{\theta_L} & \text{CRB}_{\theta_L,\Re(s)} & \text{CRB}_{\theta_L,\Im(s)} \\
\text{CRB}_{\Re(s),\theta_E} & \text{CRB}_{\Re(s),\theta_L} & \text{CRB}_{\Re(s)} & \text{CRB}_{\Re(s),\Im(s)} \\
\text{CRB}_{\Im(s),\theta_E} & \text{CRB}_{\Im(s),\theta_L} & \text{CRB}_{\Im(s),\Re(s)} & \text{CRB}_{\Im(s)}
\end{bmatrix}
\end{equation}

\subsection{Individual Security Metrics}

\subsubsection{Sensing Security Metrics}

The CRB for eavesdropper angle estimation:
\begin{equation}
\text{CRB}_{\text{Eve}}(\theta_E) = [\mathbf{C}_{\text{joint}}]_{1,1}
\end{equation}

The CRB for target angle estimation:
\begin{equation}
\text{CRB}_{\text{Eve}}(\theta_L) = [\mathbf{C}_{\text{joint}}]_{2,2}
\end{equation}

Combined sensing CRB:
\begin{equation}
\text{CRB}_{\text{sensing}} = \text{CRB}_{\text{Eve}}(\theta_E) + \text{CRB}_{\text{Eve}}(\theta_L)
\end{equation}

\subsubsection{Communication Security Metrics}

The CRB for communication symbol estimation:
\begin{equation}
\text{CRB}_{\text{communication}} = \text{CRB}_{\text{Eve}}(\Re(s)) + \text{CRB}_{\text{Eve}}(\Im(s)) = [\mathbf{C}_{\text{joint}}]_{3,3} + [\mathbf{C}_{\text{joint}}]_{4,4}
\end{equation}

This represents the fundamental limit for the eavesdropper's ability to decode communication symbols.

\subsection{Unified Security Metrics}

\subsubsection{Joint Security Index (JSI)}

Following the GUIDE approach, we propose the Joint Security Index based on the determinant:
\begin{equation}
\text{JSI} = \det(\mathbf{C}_{\text{joint}})^{1/4}
\end{equation}

where the dimension is 4 (for the four parameters $\theta_E$, $\theta_L$, $\Re(s)$, $\Im(s)$).

\textbf{Interpretation}: Higher JSI indicates better security - the eavesdropper faces higher estimation uncertainty for both sensing and communication parameters.

\subsubsection{Trace-based Security Metric}

Alternatively, use the trace of the CRB matrix:
\begin{equation}
\text{Trace-CRB} = \text{tr}(\mathbf{C}_{\text{joint}}) = \text{CRB}_{\text{Eve}}(\theta_E) + \text{CRB}_{\text{Eve}}(\theta_L) + \text{CRB}_{\text{Eve}}(\Re(s)) + \text{CRB}_{\text{Eve}}(\Im(s))
\end{equation}

This serves as a joint security metric where higher values indicate better system security.

\subsubsection{Weighted Joint CRB Metric}

For system design, we can use a weighted combination:
\begin{equation}
\text{WJCRB} = w_s \cdot \text{CRB}_{\text{sensing}} + w_c \cdot \text{CRB}_{\text{communication}} + w_{coup} \cdot \text{CRB}_{\text{coupling}}
\end{equation}

where:
\begin{itemize}
    \item $w_s$ weights the importance of sensing security (angle estimation difficulty)
    \item $w_c$ weights the importance of communication security (symbol decoding difficulty)
    \item $w_{coup}$ weights the coupling effects between sensing and communication
    \item $\text{CRB}_{\text{coupling}} = \sum_{i \neq j} |[\mathbf{C}_{\text{joint}}]_{i,j}|$ captures cross-parameter coupling
    \item Higher WJCRB indicates better overall security
\end{itemize}

\subsubsection{Security Performance Ratio}

Define the security performance ratio as:
\begin{equation}
\text{SPR} = \frac{\text{CRB}_{\text{communication}}}{\text{CRB}_{\text{sensing}}}
\end{equation}

This ratio indicates the relative security between communication and sensing: higher SPR means communication is more secure relative to sensing.

\section{Practical Implementation}

\subsection{Computational Considerations}

The joint CRB computation involves:
\begin{itemize}
    \item Computing partial derivatives of the mean vector $\boldsymbol{\mu}(\boldsymbol{\phi})$
    \item Evaluating the $4 \times 4$ Fisher Information Matrix
    \item Matrix inversion to obtain the CRB matrix
    \item Extracting relevant security metrics
\end{itemize}

\subsection{Numerical Implementation}

In practice, numerical inversion (e.g., via MATLAB or Python) is used for specific parameter values. The algorithm involves:
\begin{enumerate}
    \item Define system parameters: $\alpha_E$, $\mathbf{w}$, $s$, $\sigma_z^2$, antenna configurations
    \item Compute steering vectors $\mathbf{a}(\theta_L)$ and $\mathbf{b}(\theta_E)$ and their derivatives
    \item Evaluate partial derivatives of $\boldsymbol{\mu}(\boldsymbol{\phi})$
    \item Construct the FIM using the Slepian-Bangs formula
    \item Invert FIM to obtain CRB matrix
    \item Extract security metrics (JSI, Trace-CRB, WJCRB, SPR)
\end{enumerate}

\subsection{Special Cases and Approximations}

\subsubsection{High SNR Regime}

In high SNR regimes, the FIM elements simplify:
\begin{align}
\text{FIM}_{1,1} &\approx \frac{2 |\alpha_E s (\mathbf{a}^H \mathbf{w})|^2}{\sigma_z^2} \left\| \frac{\partial \mathbf{b}}{\partial \theta_E} \right\|^2 \\
\text{FIM}_{2,2} &\approx \frac{2 |\alpha_E s|^2 N_E}{\sigma_z^2} \left\| \frac{\partial \mathbf{a}^H}{\partial \theta_L} \mathbf{w} \right\|^2 \\
\text{FIM}_{3,3} = \text{FIM}_{4,4} &\approx \frac{2}{\sigma_z^2} \| \mathbf{A} \mathbf{w} \|^2
\end{align}

\subsubsection{Weak Coupling Approximation}

When coupling between parameters is weak, the FIM becomes approximately block-diagonal:
\begin{equation}
\text{FIM} \approx \text{diag}(\text{FIM}_{1,1}, \text{FIM}_{2,2}, \text{FIM}_{3,3}, \text{FIM}_{4,4})
\end{equation}

leading to independent CRBs for each parameter.

\section{Conclusion}

This joint CRB quantifies the estimation difficulty for Eve, unifying sensing and communication security. The key contributions include:

\subsection{Theoretical Contributions}

\begin{enumerate}
    \item \textbf{Unified Parameter Vector}: Treating sensing parameters ($\theta_E$, $\theta_L$) and communication parameters ($\Re(s)$, $\Im(s)$) jointly
    \item \textbf{Complete FIM Derivation}: Detailed computation of all FIM elements including cross-coupling terms
    \item \textbf{Security Metrics}: Multiple security metrics (JSI, Trace-CRB, WJCRB, SPR) for different design objectives
    \item \textbf{Coupling Analysis}: Quantification of how sensing and communication parameter estimation interact
\end{enumerate}

\subsection{Practical Implications}

\begin{itemize}
    \item \textbf{System Design}: The joint CRB provides fundamental limits for secure ISAC system design
    \item \textbf{Performance Evaluation}: Security metrics enable quantitative assessment of eavesdropper capabilities
    \item \textbf{Optimization}: CRB-based metrics can serve as objective functions for beamforming and power allocation
    \item \textbf{Trade-off Analysis}: The framework reveals fundamental trade-offs between sensing and communication security
\end{itemize}

\subsection{Key Insights}

\begin{enumerate}
    \item \textbf{ISAC Trade-offs}: Couplings in FIM highlight fundamental trade-offs between sensing and communication security
    \item \textbf{Parameter Interdependence}: Estimation accuracy of one parameter type affects others through cross-coupling terms
    \item \textbf{Security Assessment}: Higher CRB values indicate better security against eavesdropping attacks
    \item \textbf{Design Guidance}: The framework provides principled approach to secure ISAC system design
\end{enumerate}

In practice, numerical inversion (e.g., via MATLAB or Python) is used for specific values. The joint CRB approach provides a comprehensive framework for analyzing and designing secure ISAC systems with rigorous theoretical foundations.



\end{document}
