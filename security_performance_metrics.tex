\documentclass{article}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}

% Math commands
\newcommand{\bm}[1]{\boldsymbol{#1}}
\newcommand{\tr}{\text{tr}}
\newcommand{\diag}{\text{diag}}
\newcommand{\rank}{\text{rank}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}

\title{Security Performance Metrics for ISAC Systems}
\author{ISAC Security Framework}
\date{\today}

\begin{document}
\maketitle

% 通感一体化系统安全性能指标设计

\section{SECURITY PERFORMANCE METRICS}

In this section, we develop comprehensive security performance metrics to evaluate the overall security of the monostatic ISAC system against both sensing and communication information leakage.

\subsection{Security Threat Analysis}

The monostatic ISAC system faces dual security threats:
\begin{enumerate}
    \item \textbf{Sensing Information Leakage}: The eavesdropper can estimate target location $\theta_L$ from intercepted reflected signals
    \item \textbf{Communication Information Leakage}: The eavesdropper can decode communication data $\mathbf{x}(l)$ from intercepted signals
\end{enumerate}

\subsection{Individual Security Metrics}

\subsubsection{Sensing Security Metric}

For sensing security, we use the \textbf{Sensing Secrecy Rate} based on the estimation accuracy degradation at the eavesdropper:

\begin{equation}
R_{\text{sense}} = \frac{1}{2}\log_2\left(\frac{\text{CRB}_{\text{eve}}(\theta_L)}{\text{CRB}_{\text{BS}}(\theta_L)}\right)
\end{equation}

where:
\begin{itemize}
    \item $\text{CRB}_{\text{eve}}(\theta_L)$ is the Cramér-Rao bound for target angle estimation at the eavesdropper
    \item $\text{CRB}_{\text{BS}}(\theta_L)$ is the CRB at the legitimate BS
\end{itemize}

The sensing secrecy rate measures how much worse the eavesdropper's estimation capability is compared to the legitimate receiver.

\subsubsection{Communication Security Metric}

For communication security, we use the \textbf{Communication Secrecy Rate}:

\begin{equation}
R_{\text{comm}} = \left[I(\mathbf{x}; \mathbf{y}_{\text{CU}}) - I(\mathbf{x}; \mathbf{y}_{\text{eve}})\right]^+
\end{equation}

where:
\begin{itemize}
    \item $I(\mathbf{x}; \mathbf{y}_{\text{CU}})$ is the mutual information between transmitted signal and legitimate users
    \item $I(\mathbf{x}; \mathbf{y}_{\text{eve}})$ is the mutual information between transmitted signal and eavesdropper
    \item $[a]^+ = \max(a, 0)$ denotes the positive part
\end{itemize}

\subsection{Composite Security Metrics}

\subsubsection{Weighted Security Index (WSI)}

We propose a \textbf{Weighted Security Index} that combines both security aspects:

\begin{equation}
\text{WSI} = w_s \cdot \frac{R_{\text{sense}}}{R_{\text{sense}}^{\max}} + w_c \cdot \frac{R_{\text{comm}}}{R_{\text{comm}}^{\max}}
\end{equation}

where:
\begin{itemize}
    \item $w_s, w_c \geq 0$ are weighting factors with $w_s + w_c = 1$
    \item $R_{\text{sense}}^{\max}, R_{\text{comm}}^{\max}$ are normalization factors
    \item Higher WSI indicates better overall security
\end{itemize}

\subsubsection{Security Efficiency Metric (SEM)}

To account for the trade-off between security and system performance, we define:

\begin{equation}
\text{SEM} = \frac{\text{WSI}}{\text{Performance Cost}}
\end{equation}

where the Performance Cost can be defined as:

\begin{equation}
\text{Performance Cost} = \alpha \cdot \frac{\text{CRB}_{\text{BS}}(\theta_L)}{\text{CRB}_{\text{BS}}^{\text{ref}}(\theta_L)} + \beta \cdot \frac{R_{\text{comm}}^{\text{ref}}}{R_{\text{comm}}^{\text{achieved}}}
\end{equation}

Here, $\alpha, \beta \geq 0$ are weighting factors, and the superscript "ref" denotes reference performance without security considerations.

\subsubsection{Unified Security Level (USL)}

For a single comprehensive metric, we propose the \textbf{Unified Security Level}:

\begin{equation}
\text{USL} = \min\left(\frac{R_{\text{sense}}}{R_{\text{sense}}^{\text{th}}}, \frac{R_{\text{comm}}}{R_{\text{comm}}^{\text{th}}}\right)
\end{equation}

where $R_{\text{sense}}^{\text{th}}$ and $R_{\text{comm}}^{\text{th}}$ are security thresholds. The USL represents the weakest security link in the system.

\subsection{Detailed Metric Calculations}

\subsubsection{CRB for Sensing Security}

For the legitimate BS:
\begin{equation}
\text{CRB}_{\text{BS}}(\theta_L) = \left[\mathbf{J}_{\text{BS}}(\theta_L)\right]^{-1}
\end{equation}

where the Fisher Information Matrix is:
\begin{equation}
\mathbf{J}_{\text{BS}}(\theta_L) = \frac{2}{\sigma^2_{\text{BS}}} \sum_{l=1}^L \text{Re}\left\{\frac{\partial \mathbf{g}_L^H}{\partial \theta_L} \mathbf{Q}_x \frac{\partial \mathbf{g}_L}{\partial \theta_L}\right\}
\end{equation}

For the eavesdropper:
\begin{equation}
\text{CRB}_{\text{eve}}(\theta_L) = \left[\mathbf{J}_{\text{eve}}(\theta_L)\right]^{-1}
\end{equation}

where:
\begin{equation}
\mathbf{J}_{\text{eve}}(\theta_L) = \frac{2}{\sigma^2_{\text{tar}}} \sum_{l=1}^L \text{Re}\left\{\frac{\partial \mathbf{g}_E^H}{\partial \theta_L} \mathbf{Q}_x \frac{\partial \mathbf{g}_E}{\partial \theta_L}\right\}
\end{equation}

\subsubsection{Mutual Information for Communication Security}

For legitimate users (assuming single user for simplicity):
\begin{equation}
I(\mathbf{x}; \mathbf{y}_{\text{CU}}) = \log_2 \det\left(\mathbf{I} + \frac{\mathbf{h}_{C,k} \mathbf{Q}_x \mathbf{h}_{C,k}^H}{\sigma^2_{C,k}}\right)
\end{equation}

For the eavesdropper:
\begin{equation}
I(\mathbf{x}; \mathbf{y}_{\text{eve}}) = \log_2 \det\left(\mathbf{I}_{N_E} + \frac{[\mathbf{G}_E + \mathbf{H}_{CE}] \mathbf{Q}_x [\mathbf{G}_E + \mathbf{H}_{CE}]^H}{\sigma^2_{\text{tar}}}\right)
\end{equation}

\subsection{Practical Implementation Guidelines}

\subsubsection{Metric Selection Criteria}

\begin{itemize}
    \item \textbf{Real-time applications}: Use WSI with equal weights ($w_s = w_c = 0.5$)
    \item \textbf{Sensing-critical applications}: Use WSI with $w_s > w_c$
    \item \textbf{Communication-critical applications}: Use WSI with $w_c > w_s$
    \item \textbf{Worst-case security}: Use USL
    \item \textbf{Efficiency-focused}: Use SEM
\end{itemize}

\subsubsection{Threshold Setting}

Recommended threshold values:
\begin{align}
R_{\text{sense}}^{\text{th}} &= 3 \text{ bits} \quad \text{(8:1 estimation accuracy ratio)} \\
R_{\text{comm}}^{\text{th}} &= 1 \text{ bit/s/Hz} \quad \text{(positive secrecy rate)}
\end{align}

\subsection{Security Metric Properties}

\begin{enumerate}
    \item \textbf{Monotonicity}: All metrics increase with improved security
    \item \textbf{Boundedness}: WSI $\in [0,1]$, USL $\in [0,\infty)$
    \item \textbf{Interpretability}: Higher values indicate better security
    \item \textbf{Computability}: All metrics can be computed from system parameters
\end{enumerate}

\end{document}
