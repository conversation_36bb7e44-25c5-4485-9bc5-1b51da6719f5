# 单站雷达ISAC系统的文字修改建议

## 需要修改的主要部分

### 1. 系统架构描述修改

**原文 (需要替换的部分):**
```
In this system, a radar transmitter (Radar Tx) emits sensing signals to sense a target. A separate radar receiver (Radar Rx) operates as a full-duplex integrated sensing and communication (FD-ISAC) node, receiving the reflected sensing signals from the target and simultaneously transmitting downlink communication signals to multiple communication users (CUs).
```

**修改为:**
```
In this system, a single monostatic base station (BS) operates as a full-duplex integrated sensing and communication (FD-ISAC) node. The BS simultaneously transmits sensing signals to detect targets, receives the reflected signals from targets, and provides bidirectional communication services to multiple communication users (CUs).
```

### 2. 系统组件描述修改

**原文:**
```
The radar transmitter (Tx) is equipped with NS antennas. The full-duplex (FD) base station (BS), functioning simultaneously as the radar receiver (Rx) and the ISAC transmitter, is equipped with NC antennas, which include NC,t transmit antennas and NC,r receive antennas.
```

**修改为:**
```
The monostatic ISAC base station is equipped with N antennas that serve multiple functions simultaneously: radar transmission, radar reception, communication transmission, and communication reception. The same antenna array is used for all these functions through full-duplex operation and appropriate signal processing.
```

### 3. 信号模型修改

**原文的接收信号模型:**
```
yBS(l) = GL(θL)d(l) + HSIx(l) + nBS(l)
```

**修改为 (单站雷达模型):**
```
yBS(l) = GL(θL)s(l) + HSIs(l) + nBS(l)
```

其中 `s(l) = d(l) + x(l)` 是复合的感知通信信号。

### 4. 窃听者信号模型修改

**原文:**
```
yeve(l) = GE(θ)d(l) + HCEx(l) + ztar(l)
```

**修改为:**
```
yeve(l) = GE(θ)s(l) + ztar(l)
     = GE(θ)[d(l) + x(l)] + ztar(l)
     = GE(θ)d(l) + GE(θ)x(l) + ztar(l)
```

### 5. 信道矩阵定义修改

**单站雷达的信道矩阵:**
- `GL(θL) = α(l)a(θL)a^H(θL)` (单站情况下发射角等于接收角)
- `GE(θE) = β(l)b(θe)a^H(θL)` (目标到窃听者的反射信道)

### 6. 图1的说明文字修改

**建议的图1说明:**
```
Fig. 1: The proposed monostatic FD-enabled secure ISAC system model. The monostatic BS simultaneously performs radar sensing and communication functions using the same antenna array. Blue lines represent sensing links (target reflection), green lines represent communication links, and orange lines represent potential information leakage paths to the eavesdropper.
```

## 关键技术差异说明

### 单站 vs 双站的主要区别:

1. **硬件简化**: 单站系统只需要一个基站，降低了系统复杂度
2. **自干扰增强**: 发射和接收共用天线阵列，自干扰更严重
3. **角度估计**: 单站系统中发射角等于接收角 (θt = θr)
4. **信号设计**: 需要同时优化感知和通信性能的复合信号

### 安全性考虑:

1. **集中式威胁**: 所有功能集中在一个节点，成为攻击的重点目标
2. **信号耦合**: 感知和通信信号的耦合可能增加信息泄露风险
3. **自干扰利用**: 可以利用自干扰作为额外的安全机制

## 数学模型的相应调整

### 互信息表达式需要考虑:
- 复合信号的协方差矩阵
- 单站配置下的信道相关性
- 自干扰对安全性能的影响

### 优化问题需要增加:
- 自干扰抑制约束
- 复合信号的功率分配
- 单站配置下的波束成形设计
