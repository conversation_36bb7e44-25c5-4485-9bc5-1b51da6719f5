This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.4.28)  24 JUL 2025 15:10
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**malicious_eavesdropper_analysis
(./malicious_eavesdropper_analysis.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025/texmf-dist/tex/latex/ieeetran/IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen141
\@IEEEtrantmpdimenB=\dimen142
\@IEEEtrantmpdimenC=\dimen143
\@IEEEtrantmpcountA=\count192
\@IEEEtrantmpcountB=\count193
\@IEEEtrantmpcountC=\count194
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 503.
LaTeX Font Info:    No file TUptm.fd. on input line 503.

LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 503.

-- Using 8.5in x 11in (letter) paper.
-- Using DVI output.
\@IEEEnormalsizeunitybaselineskip=\dimen144
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen145
\CLASSINFOnormalsizeunitybaselineskip=\dimen146
\IEEEnormaljot=\dimen147

LaTeX Font Warning: Font shape `TU/ptm/bx/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/bx/it' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 1090.

\IEEEquantizedlength=\dimen148
\IEEEquantizedlengthdiff=\dimen149
\IEEEquantizedtextheightdiff=\dimen150
\IEEEilabelindentA=\dimen151
\IEEEilabelindentB=\dimen152
\IEEEilabelindent=\dimen153
\IEEEelabelindent=\dimen154
\IEEEdlabelindent=\dimen155
\IEEElabelindent=\dimen156
\IEEEiednormlabelsep=\dimen157
\IEEEiedmathlabelsep=\dimen158
\IEEEiedtopsep=\skip49
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@IEEEsubequation=\count199
\abovecaptionskip=\skip50
\belowcaptionskip=\skip51
\c@figure=\count266
\c@table=\count267
\@IEEEeqnnumcols=\count268
\@IEEEeqncolcnt=\count269
\@IEEEsubeqnnumrollback=\count270
\@IEEEquantizeheightA=\dimen159
\@IEEEquantizeheightB=\dimen160
\@IEEEquantizeheightC=\dimen161
\@IEEEquantizeprevdepth=\dimen162
\@IEEEquantizemultiple=\count271
\@IEEEquantizeboxA=\box52
\@IEEEtmpitemindent=\dimen163
\IEEEPARstartletwidth=\dimen164
\c@IEEEbiography=\count272
\@IEEEtranrubishbin=\box53
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip52

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen165
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen166
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count273
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count274
\leftroot@=\count275
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count276
\DOTSCASE@=\count277
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box54
\strutbox@=\box55
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen167
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count278
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count279
\dotsspace@=\muskip17
\c@parentequation=\count280
\dspbrk@lvl=\count281
\tag@help=\toks19
\row@=\count282
\column@=\count283
\maxfields@=\count284
\andhelp@=\toks20
\eqnshift@=\dimen168
\alignsep@=\dimen169
\tagshift@=\dimen170
\tagwidth@=\dimen171
\totwidth@=\dimen172
\lineht@=\dimen173
\@envbody=\toks21
\multlinegap=\skip53
\multlinetaggap=\skip54
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/algorithms/algorithmic.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks23
)
\c@ALC@unique=\count285
\c@ALC@line=\count286
\c@ALC@rem=\count287
\c@ALC@depth=\count288
\ALC@tlm=\skip55
\algorithmicindent=\skip56
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen174
\Gin@req@width=\dimen175
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
) (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count289
\l__pdf_internal_box=\box56
\g__pdf_backend_annotation_int=\count290
\g__pdf_backend_link_int=\count291
)
No file malicious_eavesdropper_analysis.aux.
\openout1 = `malicious_eavesdropper_analysis.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 18.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 18.

-- Lines per column: 58 (exact).

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 21.


LaTeX Font Warning: Font shape `TU/ptm/m/sc' undefined
(Font)              using `TU/ptm/m/n' instead on input line 25.

LaTeX Font Info:    Trying to load font information for U+msa on input line 32.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 32.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 32.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 33.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 34.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 35.





[1


]


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 243.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 244.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 245.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 246.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 254.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 255.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 256.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 257.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 263.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 264.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 265.



[2]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 266.





[3

] (./malicious_eavesdropper_analysis.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 3646 strings out of 473832
 58142 string characters out of 5729481
 476412 words of memory out of 5000000
 26698 multiletter control sequences out of 15000+600000
 563373 words of font info for 63 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 57i,13n,65p,538b,232s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on malicious_eavesdropper_analysis.pdf (3 pages).
