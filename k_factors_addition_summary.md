# K因子表达式添加总结

## 📋 **添加内容概述**

我已经成功将K_E, K_L, K_c的详细表达式和相关分析添加到`guide_sop_feasibility_analysis.tex`文件中。

## ✅ **添加的具体内容**

### **1. 归一化Fisher信息系数定义** (第109-150行)

#### **K_E (窃听者角度估计系数)**:
```latex
K_E = \frac{2|\beta(l)|^2 |\mathbf{a}^H\mathbf{w}|^2 \|\frac{\partial\mathbf{b}}{\partial\theta_E}\|^2}{\sigma_z^2}
```

#### **K_L (目标角度估计系数)**:
```latex
K_L = \frac{2|\beta(l)|^2 \|\mathbf{b}\|^2 |\frac{\partial\mathbf{a}^H}{\partial\theta_L}\mathbf{w}|^2}{\sigma_z^2}
```

#### **K_c (通信符号估计系数)**:
```latex
K_c = \frac{2\|\mathbf{H}_{\text{eff}}\mathbf{w}\|^2}{\sigma_z^2}
```

### **2. 物理解释** (第131-139行)

添加了每个K因子的物理含义：
- K_E 依赖于窃听者阵列几何
- K_L 依赖于基站阵列几何  
- K_c 依赖于有效信道强度
- 更大的K值表示更好的估计能力（更低的安全性）

### **3. ULA阵列的具体表达式** (第141-150行)

#### **导向矢量导数**:
```latex
∂b(θ_E)/∂θ_E = jπcos(θ_E) × diag(0,1,...,N_E-1) × b(θ_E)
∂a(θ_L)/∂θ_L = jπcos(θ_L) × diag(0,1,...,N_BS-1) × a(θ_L)
```

#### **范数计算**:
```latex
||∂b/∂θ_E||² = π²cos²(θ_E) × (N_E-1)N_E(2N_E-1)/6
|∂a^H/∂θ_L w|² = π²cos²(θ_L) × |Σ(n × w_n × e^(-jπn sin(θ_L)))|²
```

### **4. 数值示例** (第152-191行)

#### **典型系统参数**:
- N_E = 8 (窃听者天线)
- N_BS = 16 (基站天线)
- |β(l)|² = 0.64 (反射系数)
- θ_E = 45°, θ_L = 30° (角度)
- 均匀波束成形

#### **示例计算结果**:
```latex
K_E ≈ 47.1
K_L ≈ [依赖于具体波束成形]
K_c ≈ 2 × ||H_eff w||²
```

### **5. MATLAB实现代码** (第175-189行)

```matlab
function [K_E, K_L, K_c] = compute_K_factors(N_E, N_BS, beta, ...
                                           theta_E, theta_L, w, H_eff, sigma_z2)
    % 导向矢量
    b = exp(1j*pi*(0:N_E-1)'*sin(theta_E));
    a = exp(1j*pi*(0:N_BS-1)'*sin(theta_L));
    
    % 导数计算
    db_dtheta = 1j*pi*cos(theta_E) * diag(0:N_E-1) * b;
    da_dtheta = 1j*pi*cos(theta_L) * diag(0:N_BS-1) * a;
    
    % K因子计算
    K_E = 2 * abs(beta)^2 * abs(a'*w)^2 * norm(db_dtheta)^2 / sigma_z2;
    K_L = 2 * abs(beta)^2 * norm(b)^2 * abs(da_dtheta'*w)^2 / sigma_z2;
    K_c = 2 * norm(H_eff*w)^2 / sigma_z2;
end
```

### **6. 设计影响分析** (第244-282行)

#### **安全增强策略**:

**减少K_E (提高感知安全性)**:
- 零化波束成形
- 人工噪声注入
- 阵列几何优化

**减少K_L (提高目标位置安全性)**:
- 定向波束成形
- 功率分配优化
- 频率分集

**减少K_c (提高通信安全性)**:
- 预编码设计
- 协作干扰
- 信道随机化

#### **权衡分析**:
```latex
感知性能 ∝ K_E, K_L (越高越好)
通信性能 ∝ K_c (越高越好)  
安全性 ∝ 1/K_E, 1/K_L, 1/K_c (K越低越好)
```

## 🎯 **添加内容的价值**

### **1. 理论完整性**:
- ✅ 提供了K因子的完整数学定义
- ✅ 建立了物理参数与估计性能的明确联系
- ✅ 给出了ULA阵列的具体计算公式

### **2. 实用性**:
- ✅ 提供了MATLAB实现代码
- ✅ 给出了数值计算示例
- ✅ 包含了实际系统参数

### **3. 设计指导**:
- ✅ 分析了K因子对系统设计的影响
- ✅ 提供了安全增强策略
- ✅ 揭示了性能与安全的权衡关系

### **4. 可操作性**:
- ✅ 代码可以直接使用
- ✅ 参数设置有明确指导
- ✅ 优化策略具体可行

## 📊 **文档结构更新**

### **新增章节**:
1. **第2.2.1节**: 归一化Fisher信息系数
2. **第2.2.2节**: 数值示例
3. **第2.3节**: K因子的设计影响

### **增强内容**:
- 数学表达式更加完整
- 物理解释更加清晰
- 实现指导更加具体
- 设计策略更加实用

## 🚀 **后续应用**

这些K因子表达式现在可以用于：

1. **系统分析**: 定量评估不同配置的安全性
2. **优化设计**: 作为目标函数或约束条件
3. **参数调优**: 指导波束成形和功率分配
4. **性能预测**: 预估不同场景下的安全性能

添加的内容使得文档更加完整和实用，为FD-ISAC系统的安全设计提供了坚实的理论基础和实践指导。
