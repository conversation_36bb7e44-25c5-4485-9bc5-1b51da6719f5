\documentclass[journal]{IEEEtran}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}

% Math commands
\newcommand{\bm}[1]{\boldsymbol{#1}}
\newcommand{\tr}{\text{tr}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}

\title{Information Leakage Analysis for Malicious Eavesdropper in Dual-Function ISAC Systems}
\author{Based on FD\_dual\_secure\_ISAC\_monostatic and SMI Analysis}

\begin{document}
\maketitle

\begin{abstract}
This document analyzes the information leakage capability of a malicious multi-antenna eavesdropper in a dual-function integrated sensing and communication (ISAC) system. The eavesdropper can intercept both target reflection signals (sensing information) and direct communication signals. We derive comprehensive metrics to quantify the eavesdropper's ability to extract target location and communication information using sensing mutual information (SMI) and communication mutual information frameworks.
\end{abstract}

\section{System Model and Signal Structure}

\subsection{ISAC System Architecture}

We consider a monostatic full-duplex ISAC system comprising:

\begin{itemize}
    \item \textbf{Monostatic ISAC Base Station}: Equipped with $N_{BS,t}$ transmit antennas and $N_{BS,r}$ receive antennas, serving dual radar and communication functions
    \item \textbf{Communication Users}: $K$ single-antenna legitimate users receiving downlink data
    \item \textbf{Sensing Target}: A passive reflector within the radar coverage area
    \item \textbf{Malicious Eavesdropper}: An adversarial terminal with $N_E$ antennas attempting to intercept both sensing and communication information
\end{itemize}

\subsection{Dual-Function Signal Structure}

The monostatic BS transmits a composite signal vector at time slot $l$:
\begin{equation}
\mathbf{x}(l) = \mathbf{F}\mathbf{s}(l) \in \mathbb{C}^{N_{BS,t} \times 1}
\label{eq:transmitted_signal}
\end{equation}

where $\mathbf{F} \in \mathbb{C}^{N_{BS,t} \times N_D}$ is the precoding matrix and $\mathbf{s}(l) \in \mathbb{C}^{N_D \times 1}$ contains the data symbols.

\subsubsection{Precoding Matrix Structure}

The precoding matrix $\mathbf{F}$ has a partitioned structure to serve both communication and sensing functions:
\begin{equation}
\mathbf{F} = [\mathbf{F}_C \mid \mathbf{F}_S] \in \mathbb{C}^{N_{BS,t} \times N_D}
\label{eq:precoding_structure}
\end{equation}

where:
\begin{itemize}
    \item $\mathbf{F}_C \in \mathbb{C}^{N_{BS,t} \times K}$ is the communication precoding matrix for $K$ single-antenna users
    \item $\mathbf{F}_S \in \mathbb{C}^{N_{BS,t} \times N_S}$ is the sensing precoding matrix with $N_S$ sensing streams
    \item $N_D = K + N_S$ is the total number of data streams
\end{itemize}

The corresponding data vector is partitioned as:
\begin{equation}
\mathbf{s}(l) = \begin{bmatrix} \mathbf{s}_C(l) \\ \mathbf{s}_S(l) \end{bmatrix} \in \mathbb{C}^{N_D \times 1}
\label{eq:data_structure}
\end{equation}

where $\mathbf{s}_C(l) \in \mathbb{C}^{K \times 1}$ contains communication symbols for legitimate users and $\mathbf{s}_S(l) \in \mathbb{C}^{N_S \times 1}$ contains sensing waveforms.

\subsubsection{Signal Model for Random Analysis}

For theoretical analysis with random signals, we model the data symbols as independent Gaussian random variables:
\begin{equation}
\mathbf{s}(l) \sim \mathcal{CN}\left(\mathbf{0}, \frac{1}{N_{CPI}}\mathbf{I}_{N_D}\right)
\label{eq:gaussian_signal}
\end{equation}

where $N_{CPI}$ is the number of samples in one coherent processing interval (CPI), ensuring $\mathbb{E}[\mathbf{s}(l)\mathbf{s}^H(l)] = \frac{1}{N_{CPI}}\mathbf{I}_{N_D}$.

\subsection{Channel Models}

\subsubsection{Eavesdropper Reception Model}

The malicious eavesdropper receives signals through two distinct paths:
\begin{equation}
\mathbf{y}_{\text{eve}}(l) = \mathbf{G}_E(\boldsymbol{\theta})\mathbf{x}(l) + \mathbf{H}_{CE}^H\mathbf{x}(l) + \mathbf{z}_{\text{eve}}(l)
\label{eq:eavesdropper_signal}
\end{equation}

where:
\begin{itemize}
    \item $\mathbf{G}_E(\boldsymbol{\theta}) = \beta(l) \mathbf{b}(\theta_E) \mathbf{a}_t^H(\theta_L)$ represents the target-reflected sensing channel
    \item $\mathbf{H}_{CE}^H \in \mathbb{C}^{N_E \times N_{BS,t}}$ denotes the direct communication channel from BS to eavesdropper
    \item $\boldsymbol{\theta} = [\theta_E, \theta_L]^T$ contains the eavesdropper and target angles
    \item $\mathbf{z}_{\text{eve}}(l) \sim \mathcal{CN}(\mathbf{0}, \sigma_z^2 \mathbf{I}_{N_E})$ is additive white Gaussian noise
\end{itemize}

The effective channel matrix is:
\begin{equation}
\mathbf{H}_{\text{eff}} = \mathbf{G}_E(\boldsymbol{\theta}) + \mathbf{H}_{CE}^H \in \mathbb{C}^{N_E \times N_{BS,t}}
\label{eq:effective_channel}
\end{equation}

\subsubsection{Array Steering Vectors}

For uniform linear arrays (ULA) with half-wavelength spacing, the steering vectors are:
\begin{align}
\mathbf{a}_t(\theta_L) &= \left[e^{-j\frac{N_{BS,t}-1}{2}\pi\sin\theta_L}, \ldots, e^{j\frac{N_{BS,t}-1}{2}\pi\sin\theta_L}\right]^T \label{eq:bs_steering} \\
\mathbf{b}(\theta_E) &= \left[e^{-j\frac{N_E-1}{2}\pi\sin\theta_E}, \ldots, e^{j\frac{N_E-1}{2}\pi\sin\theta_E}\right]^T \label{eq:eve_steering}
\end{align}

The derivatives required for Fisher Information Matrix computation are:
\begin{align}
\frac{\partial \mathbf{a}_t(\theta_L)}{\partial \theta_L} &= j\pi\cos(\theta_L) \cdot \text{diag}\left(-\frac{N_{BS,t}-1}{2}, \ldots, \frac{N_{BS,t}-1}{2}\right) \mathbf{a}_t(\theta_L) \label{eq:bs_derivative} \\
\frac{\partial \mathbf{b}(\theta_E)}{\partial \theta_E} &= j\pi\cos(\theta_E) \cdot \text{diag}\left(-\frac{N_E-1}{2}, \ldots, \frac{N_E-1}{2}\right) \mathbf{b}(\theta_E) \label{eq:eve_derivative}
\end{align}



\section{Information Leakage Analysis}

\subsection{Joint Parameter Estimation by Eavesdropper}

The eavesdropper attempts to jointly estimate:
\begin{equation}
\boldsymbol{\phi} = [\theta_E, \theta_L, \Re(s), \Im(s)]^T
\label{eq:joint_parameters}
\end{equation}

where $\theta_E$ and $\theta_L$ represent sensing information (target location), and $\Re(s), \Im(s)$ represent communication information.

\subsection{Sensing Information Leakage}

\subsubsection{Sensing Mutual Information with Random Signals}

Following the SMI framework for random signals, the sensing mutual information between the target response and received signals is:

\begin{equation}
I_{\text{sensing}} = I(\mathbf{h}_s; \mathbf{y}_{\text{eve}} | \mathbf{S})
\label{eq:sensing_mi}
\end{equation}

where $\mathbf{h}_s = \text{vec}(\mathbf{G}_E^H)$ represents the vectorized target response matrix.

For the asymptotic case with large $N_S$, we have:
\begin{equation}
I_{\text{sensing}} = \sum_{j=1}^{K_R} \bar{\varrho}_j(\boldsymbol{\Phi}) + \mathcal{O}\left(\frac{1}{N_S}\right)
\label{eq:sensing_mi_asymptotic}
\end{equation}

where:
\begin{align}
\bar{\varrho}_j(\boldsymbol{\Phi}) &= \log_2\left|\mathbf{I}_{N_T} + \frac{\lambda_{R,j}}{1 + \lambda_{R,j}\delta(\lambda_{R,j})}\mathbf{T}(\boldsymbol{\Phi})\right| \nonumber \\
&\quad + N_S \log_2(1 + \lambda_{R,j}\delta(\lambda_{R,j})) \nonumber \\
&\quad - \frac{N_S \lambda_{R,j}\delta(\lambda_{R,j})}{1 + \lambda_{R,j}\delta(\lambda_{R,j})}
\label{eq:sensing_mi_component}
\end{align}

with $\mathbf{T}(\boldsymbol{\Phi}) = \mathbf{R}_T^{1/2} \boldsymbol{\Phi} \mathbf{R}_T^{1/2}$ and $\boldsymbol{\Phi} = \mathbf{F}\mathbf{F}^H$.

\subsubsection{Target Location Estimation Capability}

The eavesdropper's capability to estimate target angles is characterized by the Cramér-Rao bound:

\begin{align}
\text{CRB}(\theta_E) &\approx \frac{\sigma_z^2}{2|\beta(l)|^2 |\mathbf{a}^H\mathbf{w}|^2 \|\frac{\partial\mathbf{b}}{\partial\theta_E}\|^2} \\
\text{CRB}(\theta_L) &\approx \frac{\sigma_z^2}{2|\beta(l)|^2 \|\mathbf{b}\|^2 |\frac{\partial\mathbf{a}^H}{\partial\theta_L}\mathbf{w}|^2}
\end{align}

The sensing information leakage metric is:
\begin{equation}
\mathcal{L}_{\text{sensing}} = \frac{1}{\text{CRB}(\theta_E)} + \frac{1}{\text{CRB}(\theta_L)}
\label{eq:sensing_leakage}
\end{equation}

Higher values indicate greater information leakage (better eavesdropper capability).

\subsection{Communication Information Leakage}

\subsubsection{Communication Mutual Information}

The communication mutual information at the eavesdropper is:
\begin{equation}
I_{\text{comm}} = I(\mathbf{S}; \mathbf{y}_{\text{eve}} | \mathbf{H}_{\text{eff}})
\label{eq:comm_mi}
\end{equation}

For Gaussian signals, this becomes:
\begin{equation}
I_{\text{comm}} = \log_2\left|\mathbf{I} + \frac{1}{N_S \sigma_z^2} \mathbf{H}_{\text{eff}} \mathbf{F} \mathbf{F}^H \mathbf{H}_{\text{eff}}^H\right|
\label{eq:comm_mi_explicit}
\end{equation}

\subsubsection{Symbol Estimation Capability}

The eavesdropper's symbol estimation capability is characterized by:
\begin{equation}
\text{CRB}(\Re(s)) = \text{CRB}(\Im(s)) \approx \frac{\sigma_z^2}{2\|\mathbf{H}_{\text{eff}}\mathbf{w}\|^2}
\label{eq:symbol_crb}
\end{equation}

The communication information leakage metric is:
\begin{equation}
\mathcal{L}_{\text{comm}} = \frac{2}{\text{CRB}(\Re(s)) + \text{CRB}(\Im(s))} = \frac{\|\mathbf{H}_{\text{eff}}\mathbf{w}\|^2}{\sigma_z^2}
\label{eq:comm_leakage}
\end{equation}

\subsection{Joint Information Leakage Analysis}

\subsubsection{Coupling Between Sensing and Communication}

The coupling between sensing and communication information leakage is captured by the off-diagonal terms of the Fisher Information Matrix:

\begin{equation}
\text{FIM}_{i,j} = \frac{2}{\sigma_z^2} \Re\left\{\left(\frac{\partial \boldsymbol{\mu}}{\partial \phi_i}\right)^H \frac{\partial \boldsymbol{\mu}}{\partial \phi_j}\right\}
\label{eq:fim_coupling}
\end{equation}

where $\boldsymbol{\mu} = \mathbf{H}_{\text{eff}} \mathbf{w} s$ is the mean of the received signal.

\subsubsection{Joint Information Leakage Metric}

We define a comprehensive information leakage metric:
\begin{equation}
\mathcal{L}_{\text{joint}} = w_s \mathcal{L}_{\text{sensing}} + w_c \mathcal{L}_{\text{comm}} + w_{coup} \mathcal{L}_{\text{coupling}}
\label{eq:joint_leakage}
\end{equation}

where:
\begin{itemize}
    \item $w_s, w_c, w_{coup}$ are weighting factors with $w_s + w_c + w_{coup} = 1$
    \item $\mathcal{L}_{\text{coupling}} = \sum_{i \neq j} |\text{CRB}_{i,j}|$ captures cross-parameter coupling
\end{itemize}

\section{Eavesdropper Capability Assessment}

\subsection{Information Extraction Efficiency}

The eavesdropper's overall information extraction efficiency is:
\begin{equation}
\eta_{\text{eve}} = \frac{I_{\text{sensing}} + I_{\text{comm}}}{I_{\text{max}}}
\label{eq:extraction_efficiency}
\end{equation}

where $I_{\text{max}}$ is the theoretical maximum information available.

\subsection{Security Vulnerability Metrics}

\subsubsection{Sensing Vulnerability}
\begin{equation}
V_{\text{sensing}} = \frac{I_{\text{sensing}}}{\log_2|\mathbf{R}_{\text{target}}|}
\label{eq:sensing_vulnerability}
\end{equation}

\subsubsection{Communication Vulnerability}
\begin{equation}
V_{\text{comm}} = \frac{I_{\text{comm}}}{\log_2|\mathbf{Q}_{\text{signal}}|}
\label{eq:comm_vulnerability}
\end{equation}

\subsubsection{Joint Vulnerability Index}
\begin{equation}
V_{\text{joint}} = \sqrt{V_{\text{sensing}}^2 + V_{\text{comm}}^2 + 2\rho V_{\text{sensing}} V_{\text{comm}}}
\label{eq:joint_vulnerability}
\end{equation}

where $\rho$ is the correlation coefficient between sensing and communication vulnerabilities.

\section{Numerical Analysis Framework}

\subsection{System Parameters}

Consider a typical ISAC system with:
\begin{itemize}
    \item $N_{BS,t} = 16$ transmit antennas
    \item $N_E = 8$ eavesdropper antennas
    \item $N_S = 64$ samples per CPI
    \item $N_D = 4$ data streams
    \item SNR range: -10 to 30 dB
\end{itemize}

\subsection{Performance Metrics}

\begin{enumerate}
    \item \textbf{Sensing Information Leakage}: $I_{\text{sensing}}$ vs SNR
    \item \textbf{Communication Information Leakage}: $I_{\text{comm}}$ vs SNR
    \item \textbf{Joint Vulnerability}: $V_{\text{joint}}$ vs system parameters
    \item \textbf{Estimation Accuracy}: CRB values for different parameters
\end{enumerate}

\section{Key Findings and Implications}

\subsection{Critical Observations}

\begin{enumerate}
    \item \textbf{Dual Vulnerability}: The eavesdropper can exploit both sensing reflections and direct communication paths
    \item \textbf{Information Coupling}: Sensing and communication information are inherently coupled in ISAC systems
    \item \textbf{SNR Dependence}: Information leakage increases significantly with SNR
    \item \textbf{Array Advantage}: Multiple antennas provide substantial information extraction capability
\end{enumerate}

\subsection{Security Implications}

\begin{itemize}
    \item \textbf{Target Location Privacy}: Vulnerable to inference through reflection analysis
    \item \textbf{Communication Secrecy}: Direct signal interception poses significant threat
    \item \textbf{Joint Attack Capability}: Simultaneous extraction of both information types
    \item \textbf{System Design Impact}: Need for security-aware ISAC design
\end{itemize}

\section{Conclusion}

This analysis reveals that malicious eavesdroppers in ISAC systems pose significant security threats due to their ability to extract both sensing and communication information. The derived metrics provide a comprehensive framework for assessing information leakage and designing countermeasures. Future work should focus on developing security enhancement techniques to mitigate these vulnerabilities while maintaining ISAC system performance.

\end{document}
