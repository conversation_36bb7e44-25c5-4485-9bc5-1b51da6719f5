\documentclass{article}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}

% Math commands
\newcommand{\bm}[1]{\boldsymbol{#1}}
\newcommand{\tr}{\text{tr}}
\newcommand{\diag}{\text{diag}}
\newcommand{\rank}{\text{rank}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}

\title{Monostatic ISAC System Description}
\author{ISAC Security Framework}
\date{\today}

\begin{document}
\maketitle

% 修改后的单站雷达ISAC系统描述

\section{SIGNAL MODEL}

We consider a monostatic FD-ISAC framework as depicted in Figure 1. In this system, a single dual-functional base station operates as both radar transmitter and receiver while simultaneously providing communication services to multiple users.

\subsection{System Architecture}

The proposed monostatic secure ISAC system consists of the following components:

\begin{itemize}
    \item \textbf{Monostatic ISAC Base Station (BS):} A single full-duplex base station equipped with $N$ antennas that simultaneously:
    \begin{itemize}
        \item Transmits sensing signals for target detection
        \item Receives reflected signals from the target
        \item Transmits downlink communication signals to legitimate users
        \item Receives uplink communication signals from users
    \end{itemize}
    
    \item \textbf{Target:} A passive object within the sensing range that reflects the transmitted radar signals back to the BS.
    
    \item \textbf{Communication Users (CUs):} $K$ single-antenna legitimate communication users that receive downlink data from the BS.
    
    \item \textbf{Eavesdropper (Eve):} A malicious terminal equipped with $N_E$ antennas positioned within the coverage area, capable of intercepting both:
    \begin{itemize}
        \item Reflected sensing signals from the target (sensing data leakage)
        \item Downlink communication signals intended for legitimate users (communication data leakage)
    \end{itemize}
\end{itemize}

\subsection{Signal Model}

In the monostatic configuration, the BS transmits a composite signal that serves both sensing and communication purposes. Let $\mathbf{s}(l) \in \mathbb{C}^{N \times 1}$ denote the transmitted signal vector at time slot $l$, which can be decomposed as:

\begin{equation}
\mathbf{s}(l) = \mathbf{d}(l) + \mathbf{x}(l)
\end{equation}

where $\mathbf{d}(l) \in \mathbb{C}^{N \times 1}$ is the dedicated sensing signal and $\mathbf{x}(l) \in \mathbb{C}^{N \times 1}$ is the communication signal.

\subsubsection{Received Signal at Monostatic BS}

The received signal at the monostatic BS (radar receiver) is:

\begin{equation}
\mathbf{y}_{\text{BS}}(l) = \mathbf{G}_L(\theta_L) \mathbf{s}(l) + \mathbf{H}_{\text{SI}} \mathbf{s}(l) + \mathbf{n}_{\text{BS}}(l)
\end{equation}

where:
\begin{itemize}
    \item $\mathbf{G}_L(\theta_L) = \alpha(l) \mathbf{a}(\theta_t) \mathbf{a}^H(\theta_r) \in \mathbb{C}^{N \times N}$ represents the target reflection channel
    \item $\alpha(l)$ is the complex reflection coefficient
    \item $\theta_t$ and $\theta_r$ are the transmit and receive angles (equal in monostatic case: $\theta_t = \theta_r = \theta_L$)
    \item $\mathbf{a}(\theta) \in \mathbb{C}^{N \times 1}$ is the array steering vector
    \item $\mathbf{H}_{\text{SI}} \in \mathbb{C}^{N \times N}$ is the self-interference channel matrix
    \item $\mathbf{n}_{\text{BS}}(l) \sim \mathcal{CN}(\mathbf{0}, \sigma^2_{\text{BS}} \mathbf{I}_N)$ is the additive noise
\end{itemize}

\subsubsection{Received Signal at Communication Users}

The received signal at the $k$-th communication user is:

\begin{equation}
y_{C,k}(l) = \mathbf{h}^H_{C,k} \mathbf{x}(l) + z_{C,k}(l)
\end{equation}

where:
\begin{itemize}
    \item $\mathbf{h}_{C,k} \in \mathbb{C}^{N \times 1}$ is the channel vector from BS to user $k$
    \item $z_{C,k}(l) \sim \mathcal{CN}(0, \sigma^2_{C,k})$ is the noise at user $k$
\end{itemize}

\subsubsection{Received Signal at Eavesdropper}

The malicious eavesdropper receives:

\begin{equation}
\mathbf{y}_{\text{eve}}(l) = \mathbf{G}_E(\theta_E) \mathbf{s}(l) + \mathbf{H}_{CE} \mathbf{x}(l) + \mathbf{z}_{\text{tar}}(l)
\end{equation}

where:
\begin{itemize}
    \item $\mathbf{G}_E(\theta_E) = \beta(l) \mathbf{b}(\theta_e) \mathbf{a}^H(\theta_t) \in \mathbb{C}^{N_E \times N}$ is the target-to-eavesdropper channel
    \item $\beta(l)$ is the complex reflection coefficient toward the eavesdropper
    \item $\theta_e$ is the angle of arrival at the eavesdropper
    \item $\mathbf{b}(\theta_e) \in \mathbb{C}^{N_E \times 1}$ is the eavesdropper's array steering vector
    \item $\mathbf{H}_{CE} \in \mathbb{C}^{N_E \times N}$ is the direct channel from BS to eavesdropper
    \item $\mathbf{z}_{\text{tar}}(l) \sim \mathcal{CN}(\mathbf{0}, \sigma^2_{\text{tar}} \mathbf{I}_{N_E})$ is the noise
\end{itemize}

\subsection{Security Threats in Monostatic ISAC}

In the monostatic configuration, the eavesdropper faces two potential information leakage paths:

\begin{enumerate}
    \item \textbf{Sensing Information Leakage:} The eavesdropper can intercept the target-reflected signals and potentially estimate:
    \begin{itemize}
        \item Target location and angle $\theta_L$
        \item Target characteristics (RCS, velocity)
        \item Sensing signal structure
    \end{itemize}
    
    \item \textbf{Communication Information Leakage:} The eavesdropper can intercept the direct communication signals and estimate:
    \begin{itemize}
        \item Communication data symbols
        \item Channel state information
        \item User locations
    \end{itemize}
\end{enumerate}

The key advantage of the monostatic configuration is the simplified hardware architecture and reduced self-interference compared to bistatic systems, while the main challenge is the increased vulnerability to eavesdropping due to the co-located transmit and receive functions.

\end{document}

