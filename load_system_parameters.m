function params = load_system_parameters()
% LOAD_SYSTEM_PARAMETERS - Load system parameters for FD-ISAC simulation
% Based on the parameter settings defined in simulation_parameters.tex

%% ========================================================================
%% SYSTEM CONFIGURATION PARAMETERS
%% ========================================================================

% Antenna Configuration
params.N_BS = 16;           % Number of antennas at base station
params.N_E = 8;             % Number of antennas at eavesdropper
params.N_users = 3;         % Number of communication users

% Time and Frequency Parameters
params.L = 10;              % Number of time slots for analysis
params.fc = 28e9;           % Carrier frequency (28 GHz)
params.lambda = 3e8 / params.fc;  % Wavelength

%% ========================================================================
%% GEOMETRIC CONFIGURATION
%% ========================================================================

% Target and Eavesdropper Angles
params.theta_L = pi/4;      % Target angle (45 degrees)
params.theta_E = pi/3;      % Eavesdropper angle (60 degrees)

% Distance Parameters
params.d_target = 1000;     % Distance to target (meters)
params.d_eavesdropper = 500; % Distance to eavesdropper (meters)

% Array Geometry
params.d_antenna = params.lambda / 2;  % Inter-antenna spacing (half wavelength)

%% ========================================================================
%% CHANNEL PARAMETERS
%% ========================================================================

% Reflection Coefficient
params.beta_magnitude = 0.8;  % Magnitude of reflection coefficient β(l)
params.beta_phase_var = 0.1;  % Phase variation of β(l)

% Path Loss Parameters
params.path_loss_exp = 2.5;   % Path loss exponent
params.shadowing_std = 8;     % Shadowing standard deviation (dB)

% Channel Correlation
params.spatial_corr = 0.7;    % Spatial correlation coefficient
params.temporal_corr = 0.9;   % Temporal correlation coefficient

%% ========================================================================
%% SIGNAL PARAMETERS
%% ========================================================================

% Power Allocation
params.P_total = 1;           % Total transmit power (normalized)
params.P_sense_ratio = 0.6;   % Fraction of power for sensing
params.P_comm_ratio = 0.4;    % Fraction of power for communication

% Signal Covariance Matrix Design
% Uniform power allocation across antennas
params.Q_x = (params.P_total / params.N_BS) * eye(params.N_BS);

% Alternative: Beamforming-based covariance
% a_target = exp(1j * pi * (0:params.N_BS-1)' * sin(params.theta_L));
% params.Q_x = params.P_total * (a_target * a_target') / params.N_BS;

%% ========================================================================
%% NOISE AND SNR PARAMETERS
%% ========================================================================

% SNR Range for Simulation
params.SNR_dB_range = 0:2:30;  % SNR range from 0 to 30 dB

% Noise Parameters
params.noise_figure = 7;      % Noise figure (dB)
params.T_noise = 290;         % Noise temperature (Kelvin)
params.bandwidth = 100e6;     % System bandwidth (100 MHz)

% Thermal noise power
k_B = 1.38e-23;  % Boltzmann constant
params.sigma_thermal_sq = k_B * params.T_noise * params.bandwidth * ...
                         10^(params.noise_figure/10);

%% ========================================================================
%% SECURITY METRIC WEIGHTS
%% ========================================================================

% FD-WILM Weights (must sum to 1)
params.w_s = 0.4;       % Weight for sensing security
params.w_c = 0.4;       % Weight for communication security  
params.w_coup = 0.2;    % Weight for coupling penalty

%% ========================================================================
%% SIMULATION PARAMETERS
%% ========================================================================

% Monte Carlo Parameters
params.num_monte_carlo = 1000;  % Number of Monte Carlo runs
params.random_seed = 42;        % Random seed for reproducibility

% Convergence Parameters
params.tolerance = 1e-6;        % Convergence tolerance
params.max_iterations = 100;    % Maximum iterations for optimization

% Discretization Parameters
params.theta_grid_size = 180;   % Number of angle grid points
params.theta_grid = linspace(0, 2*pi, params.theta_grid_size);

%% ========================================================================
%% OPTIMIZATION PARAMETERS
%% ========================================================================

% Constraints for Signal Design Optimization
params.SINR_min = 10;           % Minimum SINR requirement (dB)
params.CRB_max = 1e-3;          % Maximum allowable CRB for sensing
params.P_sense_min = 0.1;       % Minimum sensing power

% SCA Parameters
params.sca_max_iter = 50;       % Maximum SCA iterations
params.sca_tolerance = 1e-4;    % SCA convergence tolerance

%% ========================================================================
%% VALIDATION PARAMETERS
%% ========================================================================

% Sanity Check Parameters
params.validate_results = true;  % Enable result validation
params.plot_intermediate = false; % Plot intermediate results
params.save_detailed = true;     % Save detailed results

% Expected Value Ranges (for validation)
params.expected_joint_MI_range = [0, 50];     % Expected joint MI range (bits)
params.expected_JILI_range = [0, 1];          % Expected JILI range
params.expected_coupling_range = [-10, 10];   % Expected coupling range

%% ========================================================================
%% DISPLAY PARAMETERS
%% ========================================================================

fprintf('=== FD-ISAC System Parameters ===\n');
fprintf('Base Station Antennas: %d\n', params.N_BS);
fprintf('Eavesdropper Antennas: %d\n', params.N_E);
fprintf('Time Slots: %d\n', params.L);
fprintf('Target Angle: %.1f degrees\n', params.theta_L * 180/pi);
fprintf('Eavesdropper Angle: %.1f degrees\n', params.theta_E * 180/pi);
fprintf('Total Power: %.2f\n', params.P_total);
fprintf('SNR Range: %.1f to %.1f dB\n', min(params.SNR_dB_range), max(params.SNR_dB_range));
fprintf('Monte Carlo Runs: %d\n', params.num_monte_carlo);
fprintf('Security Weights: w_s=%.1f, w_c=%.1f, w_coup=%.1f\n', ...
        params.w_s, params.w_c, params.w_coup);
fprintf('================================\n\n');

%% ========================================================================
%% PARAMETER VALIDATION
%% ========================================================================

% Validate parameter consistency
assert(abs(params.w_s + params.w_c + params.w_coup - 1) < 1e-10, ...
       'Security weights must sum to 1');
assert(params.P_sense_ratio + params.P_comm_ratio <= 1, ...
       'Power allocation ratios cannot exceed 1');
assert(params.N_BS > 0 && params.N_E > 0, ...
       'Number of antennas must be positive');
assert(params.L > 0, 'Number of time slots must be positive');
assert(all(params.SNR_dB_range >= -20 & params.SNR_dB_range <= 50), ...
       'SNR range should be reasonable (-20 to 50 dB)');

% Set random seed for reproducibility
if params.random_seed > 0
    rng(params.random_seed);
    fprintf('Random seed set to %d for reproducibility\n', params.random_seed);
end

end
