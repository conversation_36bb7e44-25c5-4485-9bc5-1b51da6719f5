function fig_to_pdf(fig_filename)
% FIG_TO_PDF - Convert FIG file to PDF format
% Usage: fig_to_pdf('filename.fig')

if ~exist(fig_filename, 'file')
    error('FIG file not found: %s', fig_filename);
end

fig_handle = openfig(fig_filename, 'invisible');
[~, name, ~] = fileparts(fig_filename);
pdf_filename = [name '.pdf'];

print(fig_handle, pdf_filename, '-dpdf', '-bestfit');
close(fig_handle);

fprintf('Converted %s to %s\n', fig_filename, pdf_filename);
end
