function metrics = compute_joint_crb_metrics(system_params, channel_params, signal_params)
% COMPUTE_JOINT_CRB_METRICS - 计算联合CRB安全指标
%
% 输入参数:
%   system_params: 系统参数结构体
%   channel_params: 信道参数结构体  
%   signal_params: 信号参数结构体
%
% 输出:
%   metrics: 联合CRB安全指标结构体

%% 提取参数
N_BS = system_params.N_BS;           % 基站天线数
N_E = system_params.N_E;             % 窃听者天线数
L = system_params.L;                 % 时隙数
theta_L = system_params.theta_L;     % 目标角度

% 信道参数
G_E = channel_params.G_E;            % 窃听者目标反射信道
H_CE = channel_params.H_CE;          % 基站到窃听者直接信道
G_L = channel_params.G_L;            % 基站目标反射信道 (用于参考)

% 信号和噪声参数
Q_x = signal_params.Q_x;             % 信号协方差矩阵
sigma_tar = signal_params.sigma_tar; % 窃听者噪声方差
sigma_BS = signal_params.sigma_BS;   % 基站噪声方差

%% 1. 计算联合Fisher信息矩阵

% 有效信道矩阵
H_eff = G_E + H_CE;

% 计算信道对角度的导数 (数值方法)
delta_theta = 1e-6;
dG_E_dtheta = compute_channel_derivative(theta_L, delta_theta, N_BS, N_E);

% 联合参数维度
dim_theta = 1;                       % 角度参数
dim_x = 2 * L * N_BS;               % 信号参数 (实部+虚部)
dim_total = dim_theta + dim_x;

% 初始化联合FIM
J_joint = zeros(dim_total, dim_total);

%% 2. 计算FIM的各个块

% 2.1 角度-角度块 J_theta_theta
J_theta_theta = 0;
for l = 1:L
    % 假设信号功率为单位功率 (可以根据实际Q_x调整)
    signal_power = trace(Q_x) / N_BS;
    J_theta_theta = J_theta_theta + (2/sigma_tar^2) * signal_power * ...
                    real(dG_E_dtheta' * dG_E_dtheta);
end

% 2.2 信号-信号块 J_x_x (块对角结构)
J_x_x = zeros(dim_x, dim_x);
H_eff_real = [real(H_eff), -imag(H_eff); imag(H_eff), real(H_eff)];
J_x_block = (2/sigma_tar^2) * (H_eff_real' * H_eff_real);

for l = 1:L
    idx_start = (l-1) * 2 * N_BS + 1;
    idx_end = l * 2 * N_BS;
    J_x_x(idx_start:idx_end, idx_start:idx_end) = J_x_block;
end

% 2.3 角度-信号耦合块 J_theta_x (简化处理，假设弱耦合)
J_theta_x = zeros(dim_theta, dim_x);
% 在实际应用中，这里需要根据具体的信号统计特性计算

% 2.4 组装联合FIM
J_joint(1, 1) = J_theta_theta;
J_joint(2:end, 2:end) = J_x_x;
J_joint(1, 2:end) = J_theta_x;
J_joint(2:end, 1) = J_theta_x';

%% 3. 计算联合CRB矩阵

% 检查FIM的条件数
cond_num = cond(J_joint);
if cond_num > 1e12
    warning('FIM is ill-conditioned. Using regularization.');
    J_joint = J_joint + 1e-10 * eye(size(J_joint));
end

% 计算CRB矩阵
C_joint = inv(J_joint);

%% 4. 提取各项CRB

% 角度估计的CRB
CRB_eve_theta = C_joint(1, 1);

% 信号估计的CRB (取迹)
CRB_eve_signal = trace(C_joint(2:end, 2:end));

%% 5. 计算参考CRB (合法接收机)

% 合法接收机的角度估计CRB
J_BS_theta = compute_legitimate_fisher(G_L, Q_x, sigma_BS, theta_L, N_BS);
CRB_BS_theta = 1 / J_BS_theta;

% 参考信号估计CRB (理想情况下的最小值)
CRB_ref_signal = dim_x * sigma_BS^2 / trace(Q_x);

%% 6. 计算联合安全指标

% 6.1 联合安全指数 (JSI)
JSI = det(C_joint)^(1/dim_total);

% 6.2 归一化联合安全指标 (NJSM)
% 构造参考CRB矩阵
C_ref = diag([CRB_BS_theta; CRB_ref_signal * ones(dim_x, 1)]);
NJSM = det(C_joint) / det(C_ref);

% 6.3 加权联合CRB指标 (WJCRB)
w_theta = 0.3;  % 角度权重
w_signal = 0.7; % 信号权重
WJCRB = w_theta * (CRB_eve_theta / CRB_BS_theta) + ...
        w_signal * (CRB_eve_signal / CRB_ref_signal);

% 6.4 简化联合指标 (单时隙近似)
if L == 1
    JSI_simple = (CRB_eve_theta * det(C_joint(2:end, 2:end)))^(1/dim_total);
else
    JSI_simple = JSI;  % 多时隙情况使用完整计算
end

%% 7. 计算传统分离指标用于对比

% 感知保密率
R_sense = 0.5 * log2(CRB_eve_theta / CRB_BS_theta);

% 通信保密率 (基于互信息的近似)
% 这里使用CRB来近似互信息差异
I_eve_approx = -0.5 * log2(det(C_joint(2:end, 2:end)) * (2*pi*exp(1))^dim_x);
I_ref_approx = -0.5 * log2(CRB_ref_signal^dim_x * (2*pi*exp(1))^dim_x);
R_comm_approx = max(0, I_ref_approx - I_eve_approx);

%% 8. 输出结果
metrics.JSI = JSI;
metrics.NJSM = NJSM;
metrics.WJCRB = WJCRB;
metrics.JSI_simple = JSI_simple;

% 分解的CRB
metrics.CRB_eve_theta = CRB_eve_theta;
metrics.CRB_eve_signal = CRB_eve_signal;
metrics.CRB_BS_theta = CRB_BS_theta;
metrics.CRB_ref_signal = CRB_ref_signal;

% 传统指标对比
metrics.R_sense = R_sense;
metrics.R_comm_approx = R_comm_approx;

% 矩阵信息
metrics.J_joint = J_joint;
metrics.C_joint = C_joint;
metrics.condition_number = cond_num;

% 安全等级评估
metrics.security_level = evaluate_joint_security_level(JSI, WJCRB);

end

%% 辅助函数

function dG_dtheta = compute_channel_derivative(theta_L, delta_theta, N_BS, N_E)
% 计算信道矩阵对角度的导数

% 数值导数计算
theta_plus = theta_L + delta_theta;
theta_minus = theta_L - delta_theta;

% 基站阵列导向矢量
a_BS_plus = exp(1j * pi * (0:N_BS-1)' * sin(theta_plus));
a_BS_minus = exp(1j * pi * (0:N_BS-1)' * sin(theta_minus));

% 窃听者阵列导向矢量 (假设固定角度)
theta_E = pi/4;  % 窃听者角度
b_E = exp(1j * pi * (0:N_E-1)' * sin(theta_E));

% 反射系数 (假设常数)
beta = 0.6 * exp(1j * pi/3);

% 信道矩阵导数
G_E_plus = beta * b_E * a_BS_plus';
G_E_minus = beta * b_E * a_BS_minus';

dG_dtheta = (G_E_plus - G_E_minus) / (2 * delta_theta);
dG_dtheta = dG_dtheta(:);  % 向量化

end

function J_BS = compute_legitimate_fisher(G_L, Q_x, sigma_BS, theta_L, N_BS)
% 计算合法接收机的Fisher信息

delta_theta = 1e-6;
theta_plus = theta_L + delta_theta;
theta_minus = theta_L - delta_theta;

% 基站阵列导向矢量导数
a_BS_plus = exp(1j * pi * (0:N_BS-1)' * sin(theta_plus));
a_BS_minus = exp(1j * pi * (0:N_BS-1)' * sin(theta_minus));
da_dtheta = (a_BS_plus - a_BS_minus) / (2 * delta_theta);

% 反射系数
alpha = 0.8 * exp(1j * pi/4);

% Fisher信息
J_BS = (2/sigma_BS^2) * real(da_dtheta' * Q_x * da_dtheta) * abs(alpha)^2;

end

function level = evaluate_joint_security_level(JSI, WJCRB)
% 评估联合安全等级

if JSI >= 1e-2 && WJCRB >= 5
    level = 'High';
elseif JSI >= 1e-3 && WJCRB >= 2
    level = 'Medium';
elseif JSI >= 1e-4 && WJCRB >= 1
    level = 'Low';
else
    level = 'Insufficient';
end

end

%% 示例使用函数
function example_joint_crb()
% 示例：联合CRB指标计算

% 系统参数
system_params.N_BS = 4;
system_params.N_E = 2;
system_params.L = 1;  % 单时隙分析
system_params.theta_L = pi/6;

% 生成信道参数
N_BS = system_params.N_BS;
N_E = system_params.N_E;
theta_L = system_params.theta_L;

% 目标反射信道
alpha = 0.8 * exp(1j * pi/4);
a_BS = exp(1j * pi * (0:N_BS-1)' * sin(theta_L));
channel_params.G_L = alpha * a_BS * a_BS';

% 窃听者信道
beta = 0.6 * exp(1j * pi/3);
theta_E = pi/4;
a_E = exp(1j * pi * (0:N_E-1)' * sin(theta_E));
channel_params.G_E = beta * a_E * a_BS';

% 直接信道
channel_params.H_CE = 0.3 * (randn(N_E, N_BS) + 1j*randn(N_E, N_BS))/sqrt(2);

% 信号参数
signal_params.Q_x = eye(N_BS);
signal_params.sigma_BS = 0.1;
signal_params.sigma_tar = 0.2;

% 计算联合CRB指标
metrics = compute_joint_crb_metrics(system_params, channel_params, signal_params);

% 显示结果
fprintf('=== 联合CRB安全指标结果 ===\n');
fprintf('联合安全指数 (JSI): %.2e\n', metrics.JSI);
fprintf('归一化联合安全指标 (NJSM): %.3f\n', metrics.NJSM);
fprintf('加权联合CRB指标 (WJCRB): %.3f\n', metrics.WJCRB);
fprintf('简化联合指标: %.2e\n', metrics.JSI_simple);
fprintf('\n--- 分解结果 ---\n');
fprintf('窃听者角度CRB: %.2e\n', metrics.CRB_eve_theta);
fprintf('窃听者信号CRB: %.2e\n', metrics.CRB_eve_signal);
fprintf('基站角度CRB: %.2e\n', metrics.CRB_BS_theta);
fprintf('\n--- 传统指标对比 ---\n');
fprintf('感知保密率: %.3f bits\n', metrics.R_sense);
fprintf('通信保密率(近似): %.3f bits\n', metrics.R_comm_approx);
fprintf('\n--- 系统状态 ---\n');
fprintf('FIM条件数: %.2e\n', metrics.condition_number);
fprintf('安全等级: %s\n', metrics.security_level);

end
