function fig_to_eps(fig_filename)
% FIG_TO_EPS - Convert FIG file to EPS format
% Usage: fig_to_eps('filename.fig')

if ~exist(fig_filename, 'file')
    error('FIG file not found: %s', fig_filename);
end

fig_handle = openfig(fig_filename, 'invisible');
[~, name, ~] = fileparts(fig_filename);
eps_filename = [name '.eps'];

print(fig_handle, eps_filename, '-depsc', '-tiff');
close(fig_handle);

fprintf('Converted %s to %s\n', fig_filename, eps_filename);
end
