\documentclass{article}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}

% Math commands
\newcommand{\bm}[1]{\boldsymbol{#1}}
\newcommand{\tr}{\text{tr}}
\newcommand{\diag}{\text{diag}}
\newcommand{\rank}{\text{rank}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}

\title{Monostatic ISAC Signal Model}
\author{ISAC Security Framework}
\date{\today}

\begin{document}
\maketitle

% 单站雷达ISAC系统的完整信号模型

\section{SIGNAL MODEL}

We consider a monostatic FD-ISAC framework as depicted in Figure 1. In this system, a single dual-functional base station operates as both radar transmitter and receiver while simultaneously providing communication services to multiple users.

\subsection{System Description}

The proposed monostatic secure ISAC system consists of the following components:

\begin{itemize}
    \item \textbf{Monostatic ISAC Base Station:} Equipped with $N$ antennas, serving as:
    \begin{itemize}
        \item Radar transmitter for target illumination
        \item Radar receiver for echo signal processing  
        \item Communication transmitter for downlink data
        \item Communication receiver for uplink data (if applicable)
    \end{itemize}
    
    \item \textbf{Target:} A passive reflector within the sensing coverage area
    
    \item \textbf{Communication Users:} $K$ single-antenna legitimate users
    
    \item \textbf{Eavesdropper:} A malicious terminal with $N_E$ antennas attempting to intercept both sensing and communication information
\end{itemize}

However, a malicious terminal, referred to as the eavesdropper (Eve), is present in the vicinity of the monostatic radar system. This adversary can exploit two vulnerabilities:
\begin{itemize}
    \item It may intercept the reflected sensing signals from the target, thereby inferring sensitive information such as the target's location (sensing data leakage).
    \item It may also eavesdrop on the downlink communication signals intended for the legitimate CUs, resulting in communication data leakage.
\end{itemize}

We assume that the Eve is equipped with $N_E$ antennas. Figure 1 illustrates these components and interactions using different types of links: sensing links (blue), communication links (green), and potential data leakage paths (orange).

The monostatic base station is equipped with $N$ antennas that simultaneously perform radar and communication functions. The system serves $K$ single-antenna CUs, which receive downlink communication signals from the BS. In addition, a target exists within the sensing range of the radar. Notably, an unauthorized malicious terminal is present in the vicinity, capable of intercepting both the radar's reflected sensing signals and the downlink communication intended for the legitimate CUs.

\subsection{Signal Model}

The monostatic BS transmits a composite signal vector $\mathbf{s}(l) \in \mathbb{C}^{N \times 1}$ at time slot $l$:

\begin{equation}
\mathbf{s}(l) = \mathbf{d}(l) + \mathbf{x}(l)
\end{equation}

where $\mathbf{d}(l) \in \mathbb{C}^{N \times 1}$ denotes the $l$-th sample of the sensing signal and $\mathbf{x}(l) \in \mathbb{C}^{N \times 1}$ is the communication signal vector.

\subsubsection{Received Signal at Monostatic BS}

The received signal at the monostatic ISAC BS can be expressed as:

\begin{equation}
\mathbf{y}_{\text{BS}}(l) = \mathbf{G}_L(\theta_L)\mathbf{s}(l) + \mathbf{H}_{\text{SI}}\mathbf{s}(l) + \mathbf{n}_{\text{BS}}(l)
\end{equation}

where $\mathbf{G}_L(\theta_L) = \alpha(l)\mathbf{a}(\theta_L)\mathbf{a}^H(\theta_L)$ represents the target reflection channel matrix for the monostatic configuration, with $\theta_L$ being the target angle (same for transmission and reception), $\mathbf{a}(\theta_L) \in \mathbb{C}^{N \times 1}$ is the array steering vector, $\mathbf{H}_{\text{SI}} \in \mathbb{C}^{N \times N}$ denotes the self-interference channel matrix, and $\mathbf{n}_{\text{BS}}(l)$ is the additive noise.

\subsubsection{Received Signal at Eavesdropper}

The received signal at the malicious terminal can be written as:

\begin{equation}
\mathbf{y}_{\text{eve}}(l) = \mathbf{G}_E(\theta_E)\mathbf{x}(l) + \mathbf{H}_{CE}\mathbf{x}(l) + \mathbf{z}_{\text{tar}}(l)
\end{equation}

where $\mathbf{G}_E(\theta_E) = \beta(l)\mathbf{b}(\theta_e)\mathbf{a}^H(\theta_L)$ represents the target-reflected channel from BS to eavesdropper, $\mathbf{H}_{CE} \in \mathbb{C}^{N_E \times N_{BS}}$ is the direct channel from BS to eavesdropper, $\theta_e$ denotes the angle of arrival at the eavesdropper, $\mathbf{b}(\theta_e) \in \mathbb{C}^{N_E \times 1}$ is the eavesdropper's array steering vector, and $\mathbf{z}_{\text{tar}}(l)$ is the additive noise.

This can be rewritten as:
\begin{equation}
\mathbf{y}_{\text{eve}}(l) = [\mathbf{G}_E(\theta_E) + \mathbf{H}_{CE}]\mathbf{x}(l) + \mathbf{z}_{\text{tar}}(l)
\end{equation}

\subsubsection{Received Signal at Communication Users}

The received symbol at the $k$-th single-antenna CU at the $l$-th time slot is given as:

\begin{equation}
y_{C,k}(l) = \mathbf{h}^H_{C,k}\mathbf{x}(l) + z_{C,k}(l)
\end{equation}

where $\mathbf{h}_{C,k} \in \mathbb{C}^{N \times 1}$ denotes the channel vector between the monostatic BS and the $k$-th CU, and $z_{C,k}[l]$ denotes the additive white Gaussian noise (AWGN) with variance $\sigma^2_{C,k}$.

\subsection{Information Leakage Analysis}

For the monostatic configuration, the information leakage can be expressed as:

\begin{equation}
I_{\text{leak}} = I(\mathbf{x}; \mathbf{y}_{\text{eve}})
\end{equation}

The time slot index $l$ is omitted for brevity:
\begin{equation}
\mathbf{y}_{\text{eve}} = [\mathbf{G}_E + \mathbf{H}_{CE}]\mathbf{x} + \mathbf{z}_{\text{tar}}
\end{equation}

where $\mathbf{x} \sim \mathcal{CN}(\mathbf{0}, \mathbf{Q}_x)$, $\mathbf{z}_{\text{tar}} \sim \mathcal{CN}(\mathbf{0}, \sigma^2_{\text{tar}}\mathbf{I}_{N_E})$, and the vectors are mutually independent.

Define the effective eavesdropping channel as:
\begin{equation}
\mathbf{H}_{\text{eff}} = \mathbf{G}_E + \mathbf{H}_{CE}
\end{equation}

The covariance matrix of $\mathbf{y}_{\text{eve}}$ is:
\begin{equation}
\boldsymbol{\Sigma}_y = \mathbf{H}_{\text{eff}}\mathbf{Q}_x\mathbf{H}_{\text{eff}}^H + \sigma^2_{\text{tar}}\mathbf{I}_{N_E}
\end{equation}

Therefore, the information leakage becomes:
\begin{equation}
I_{\text{leak}} = \log\det\left(\mathbf{I}_{N_E} + \sigma^{-2}_{\text{tar}}\mathbf{H}_{\text{eff}}\mathbf{Q}_x\mathbf{H}_{\text{eff}}^H\right)
\end{equation}

\subsection{Special Considerations for Monostatic Configuration}

\begin{enumerate}
    \item \textbf{Self-Interference:} The monostatic configuration suffers from stronger self-interference due to simultaneous transmission and reception at the same location.
    
    \item \textbf{Angle Coupling:} The transmit and receive angles are identical ($\theta_t = \theta_r = \theta_L$), which simplifies angle estimation but may reduce angular resolution.
    
    \item \textbf{Signal Coupling:} The sensing and communication signals are transmitted from the same array, leading to stronger coupling between the two functions.
    
    \item \textbf{Security Implications:} The co-location of all functions makes the system a concentrated target for eavesdropping attacks.
\end{enumerate}

\end{document}
