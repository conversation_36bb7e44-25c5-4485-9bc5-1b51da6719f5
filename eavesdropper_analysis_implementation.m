%% Malicious Eavesdropper Information Leakage Analysis
% Based on FD_dual_secure_ISAC_monostatic signal model
% Analyzes eavesdropper's capability to extract sensing and communication info

clear; close all; clc;

%% System Parameters
N_BS_t = 16;        % BS transmit antennas
N_E = 8;            % Eavesdropper antennas  
N_S = 64;           % Samples per CPI
N_D = 4;            % Data streams
K = 2;              % Communication users

% Angles
theta_L = 30;       % Target angle (degrees)
theta_E = 45;       % Eavesdropper angle (degrees)

% SNR range for analysis
SNR_dB = -10:2:30;
SNR_linear = 10.^(SNR_dB/10);

% Noise variance (normalized)
sigma_z2 = 1;

%% Channel Models

% Steering vectors (ULA with half-wavelength spacing)
a_L = exp(1j * pi * (0:N_BS_t-1)' * sin(deg2rad(theta_L)));  % Target steering vector
b_E = exp(1j * pi * (0:N_E-1)' * sin(deg2rad(theta_E)));     % Eavesdropper steering vector

% Derivatives for CRB calculation
da_dtheta_L = 1j * pi * cos(deg2rad(theta_L)) * diag(0:N_BS_t-1) * a_L;
db_dtheta_E = 1j * pi * cos(deg2rad(theta_E)) * diag(0:N_E-1) * b_E;

% Precoding matrix (uniform beamforming)
F = (1/sqrt(N_BS_t)) * eye(N_BS_t, N_D);
w = F(:,1);  % First column as beamforming vector

% Direct communication channel (Rayleigh fading)
H_CE = (randn(N_BS_t, N_E) + 1j*randn(N_BS_t, N_E)) / sqrt(2);

%% Initialize Results
I_sensing = zeros(size(SNR_dB));
I_comm = zeros(size(SNR_dB));
CRB_theta_E = zeros(size(SNR_dB));
CRB_theta_L = zeros(size(SNR_dB));
CRB_comm = zeros(size(SNR_dB));
V_joint = zeros(size(SNR_dB));

%% Analysis Loop
for idx = 1:length(SNR_dB)
    
    % Current SNR
    snr = SNR_linear(idx);
    
    % Reflection coefficient (depends on SNR)
    beta = sqrt(0.1 * snr);  % Reflection strength
    
    % Target reflection channel
    G_E = beta * b_E * a_L';
    
    % Effective channel
    H_eff = G_E + H_CE';
    
    %% Sensing Information Analysis
    
    % Sensing mutual information (simplified approximation)
    R_target = eye(N_E * N_BS_t);  % Target correlation matrix
    Phi = F * F';
    
    % Approximate sensing MI using deterministic upper bound
    I_sensing(idx) = real(log2(det(eye(N_E) + (snr/sigma_z2) * ...
                     (1/N_S) * H_eff * Phi * H_eff')));
    
    % CRB for angle estimation
    K_E = 2 * abs(beta)^2 * abs(a_L' * w)^2 * norm(db_dtheta_E)^2 / sigma_z2;
    K_L = 2 * abs(beta)^2 * norm(b_E)^2 * abs(da_dtheta_L' * w)^2 / sigma_z2;
    
    CRB_theta_E(idx) = 1 / (K_E * snr);
    CRB_theta_L(idx) = 1 / (K_L * snr);
    
    %% Communication Information Analysis
    
    % Communication mutual information
    I_comm(idx) = real(log2(det(eye(N_D) + (snr/(N_S * sigma_z2)) * ...
                  F' * H_eff' * H_eff * F)));
    
    % CRB for symbol estimation
    K_c = 2 * norm(H_eff * w)^2 / sigma_z2;
    CRB_comm(idx) = 2 / (K_c * snr);  % For both real and imaginary parts
    
    %% Joint Vulnerability Assessment
    
    % Individual vulnerabilities
    V_sensing = I_sensing(idx) / log2(det(R_target));
    V_comm = I_comm(idx) / log2(det(eye(N_D)));
    
    % Correlation coefficient (simplified)
    rho = 0.3;  % Moderate coupling
    
    % Joint vulnerability
    V_joint(idx) = sqrt(V_sensing^2 + V_comm^2 + 2*rho*V_sensing*V_comm);
    
end

%% Information Leakage Metrics

% Sensing information leakage
L_sensing = 1./CRB_theta_E + 1./CRB_theta_L;

% Communication information leakage  
L_comm = 1./CRB_comm;

% Joint information leakage (weighted combination)
w_s = 0.4; w_c = 0.4; w_coup = 0.2;
L_joint = w_s * L_sensing + w_c * L_comm + w_coup * abs(L_sensing - L_comm);

%% Plotting Results

figure('Position', [100, 100, 1200, 800]);

% Sensing Information Leakage
subplot(2,3,1);
semilogy(SNR_dB, I_sensing, 'b-o', 'LineWidth', 2, 'MarkerSize', 6);
grid on;
xlabel('SNR (dB)');
ylabel('Sensing MI (bits)');
title('Sensing Information Leakage');

% Communication Information Leakage
subplot(2,3,2);
semilogy(SNR_dB, I_comm, 'r-s', 'LineWidth', 2, 'MarkerSize', 6);
grid on;
xlabel('SNR (dB)');
ylabel('Communication MI (bits)');
title('Communication Information Leakage');

% CRB Comparison
subplot(2,3,3);
semilogy(SNR_dB, CRB_theta_E, 'g-^', 'LineWidth', 2, 'MarkerSize', 6);
hold on;
semilogy(SNR_dB, CRB_theta_L, 'm-v', 'LineWidth', 2, 'MarkerSize', 6);
semilogy(SNR_dB, CRB_comm, 'c-d', 'LineWidth', 2, 'MarkerSize', 6);
grid on;
xlabel('SNR (dB)');
ylabel('CRB');
title('Estimation Accuracy (CRB)');
legend('\theta_E', '\theta_L', 'Communication', 'Location', 'best');

% Joint Information Leakage
subplot(2,3,4);
semilogy(SNR_dB, L_joint, 'k-o', 'LineWidth', 2, 'MarkerSize', 6);
grid on;
xlabel('SNR (dB)');
ylabel('Joint Leakage Metric');
title('Joint Information Leakage');

% Vulnerability Comparison
subplot(2,3,5);
plot(SNR_dB, V_joint, 'r-', 'LineWidth', 2);
grid on;
xlabel('SNR (dB)');
ylabel('Joint Vulnerability Index');
title('Overall Security Vulnerability');

% Information Extraction Efficiency
subplot(2,3,6);
eta_eve = (I_sensing + I_comm) ./ (log2(N_E * N_BS_t) + log2(N_D));
plot(SNR_dB, eta_eve, 'b-', 'LineWidth', 2);
grid on;
xlabel('SNR (dB)');
ylabel('Extraction Efficiency');
title('Eavesdropper Information Extraction');

sgtitle('Malicious Eavesdropper Information Leakage Analysis', 'FontSize', 14, 'FontWeight', 'bold');

%% Detailed Analysis Results

fprintf('\n=== EAVESDROPPER CAPABILITY ANALYSIS ===\n');
fprintf('System Configuration:\n');
fprintf('  BS Antennas: %d\n', N_BS_t);
fprintf('  Eavesdropper Antennas: %d\n', N_E);
fprintf('  Samples per CPI: %d\n', N_S);
fprintf('  Data Streams: %d\n', N_D);
fprintf('  Target Angle: %.1f°\n', theta_L);
fprintf('  Eavesdropper Angle: %.1f°\n', theta_E);

fprintf('\nAt SNR = 20 dB:\n');
idx_20dB = find(SNR_dB == 20);
if ~isempty(idx_20dB)
    fprintf('  Sensing MI: %.3f bits\n', I_sensing(idx_20dB));
    fprintf('  Communication MI: %.3f bits\n', I_comm(idx_20dB));
    fprintf('  CRB(θ_E): %.2e\n', CRB_theta_E(idx_20dB));
    fprintf('  CRB(θ_L): %.2e\n', CRB_theta_L(idx_20dB));
    fprintf('  CRB(Communication): %.2e\n', CRB_comm(idx_20dB));
    fprintf('  Joint Vulnerability: %.3f\n', V_joint(idx_20dB));
    fprintf('  Extraction Efficiency: %.3f\n', eta_eve(idx_20dB));
end

%% Security Threat Assessment

fprintf('\n=== SECURITY THREAT ASSESSMENT ===\n');

% High SNR analysis (SNR > 15 dB)
high_snr_idx = SNR_dB > 15;

avg_sensing_MI = mean(I_sensing(high_snr_idx));
avg_comm_MI = mean(I_comm(high_snr_idx));
avg_vulnerability = mean(V_joint(high_snr_idx));

fprintf('High SNR Regime (>15 dB) Averages:\n');
fprintf('  Sensing Information Leakage: %.3f bits\n', avg_sensing_MI);
fprintf('  Communication Information Leakage: %.3f bits\n', avg_comm_MI);
fprintf('  Overall Vulnerability: %.3f\n', avg_vulnerability);

% Threat level classification
if avg_vulnerability > 0.7
    threat_level = 'CRITICAL';
elseif avg_vulnerability > 0.5
    threat_level = 'HIGH';
elseif avg_vulnerability > 0.3
    threat_level = 'MEDIUM';
else
    threat_level = 'LOW';
end

fprintf('\nThreat Level: %s\n', threat_level);

%% Countermeasure Recommendations

fprintf('\n=== COUNTERMEASURE RECOMMENDATIONS ===\n');

if avg_vulnerability > 0.5
    fprintf('1. Implement artificial noise injection\n');
    fprintf('2. Use null-steering beamforming toward eavesdropper\n');
    fprintf('3. Apply power allocation optimization\n');
    fprintf('4. Consider frequency hopping techniques\n');
end

if avg_sensing_MI > avg_comm_MI
    fprintf('5. Sensing information is more vulnerable - enhance target masking\n');
else
    fprintf('5. Communication is more vulnerable - strengthen encryption\n');
end

fprintf('6. Deploy cooperative jamming if possible\n');
fprintf('7. Optimize antenna array geometry\n');

%% Save Results

save('eavesdropper_analysis_results.mat', 'SNR_dB', 'I_sensing', 'I_comm', ...
     'CRB_theta_E', 'CRB_theta_L', 'CRB_comm', 'V_joint', 'L_joint', 'eta_eve');

fprintf('\nResults saved to: eavesdropper_analysis_results.mat\n');
fprintf('Analysis complete!\n');
