# FIG格式图表使用指南

## 📊 生成的FIG文件列表

### ✅ 成功生成的8个FIG文件：

1. **`joint_mutual_info_components.fig`** (12.49 KB)
   - 联合互信息组件分析
   - 包含：联合MI、感知MI、通信MI、耦合信息

2. **`security_metrics.fig`** (15.19 KB)
   - 安全指标可视化
   - 包含：FD-JILI和FD-WILM指标

3. **`information_leakage_ratios.fig`** (12.13 KB)
   - 信息泄露比率分析
   - 包含：感知泄露比率和通信泄露比率

4. **`coupling_analysis.fig`** (12.44 KB)
   - 耦合强度分析
   - 显示信息耦合随SNR的变化

5. **`comprehensive_security_analysis.fig`** (40.53 KB)
   - 综合安全分析（四面板）
   - 最大的文件，包含完整分析

6. **`3d_mutual_info_landscape.fig`** (17.84 KB)
   - 3D互信息景观图
   - 交互式3D可视化

7. **`summary_statistics.fig`** (16.19 KB)
   - 统计摘要图表
   - 包含均值和标准差

8. **`joint_mi_detailed_analysis.fig`** (37.21 KB)
   - 详细分析图表（原始仿真生成）

## 🔧 在MATLAB中使用FIG文件

### 基本操作：

#### 1. 打开单个FIG文件
```matlab
% 基本打开
openfig('joint_mutual_info_components.fig')

% 重用现有窗口
openfig('joint_mutual_info_components.fig', 'reuse')

% 总是创建新窗口
openfig('joint_mutual_info_components.fig', 'new')
```

#### 2. 打开所有FIG文件
```matlab
% 使用提供的工具函数
open_all_figs()
```

#### 3. 获取图形句柄进行编辑
```matlab
fig_handle = openfig('joint_mutual_info_components.fig');
% 现在可以修改图形属性
title('修改后的标题');
xlabel('新的X轴标签');
```

### 高级操作：

#### 1. 修改图形属性
```matlab
fig_handle = openfig('security_metrics.fig');

% 修改线条属性
lines = findobj(fig_handle, 'Type', 'line');
set(lines(1), 'LineWidth', 3, 'Color', 'red');

% 修改字体大小
set(findall(fig_handle, 'Type', 'text'), 'FontSize', 14);

% 保存修改后的图形
savefig(fig_handle, 'modified_security_metrics.fig');
```

#### 2. 提取数据
```matlab
fig_handle = openfig('joint_mutual_info_components.fig', 'invisible');

% 获取所有线条对象
lines = findobj(fig_handle, 'Type', 'line');

% 提取第一条线的数据
x_data = get(lines(1), 'XData');
y_data = get(lines(1), 'YData');

% 关闭不可见的图形
close(fig_handle);
```

## 🔄 格式转换

### 使用提供的转换函数：

#### 1. 转换为PNG（高分辨率）
```matlab
% 默认300 DPI
fig_to_png('joint_mutual_info_components.fig')

% 自定义分辨率
fig_to_png('joint_mutual_info_components.fig', 'resolution', 600)
```

#### 2. 转换为PDF
```matlab
fig_to_pdf('comprehensive_security_analysis.fig')
```

#### 3. 转换为EPS
```matlab
fig_to_eps('3d_mutual_info_landscape.fig')
```

### 批量转换：
```matlab
% 获取所有FIG文件
fig_files = dir('*.fig');

% 批量转换为PNG
for i = 1:length(fig_files)
    fig_to_png(fig_files(i).name);
end
```

## 📝 自定义和编辑

### 1. 添加注释和标记
```matlab
fig_handle = openfig('coupling_analysis.fig');

% 添加文本注释
text(15, 0.5, '重要观察点', 'FontSize', 12, 'Color', 'red');

% 添加箭头
annotation('arrow', [0.3, 0.4], [0.6, 0.7]);

% 添加矩形框
rectangle('Position', [10, -0.1, 5, 0.2], 'EdgeColor', 'blue');
```

### 2. 创建子图组合
```matlab
% 打开多个FIG文件并组合
fig1 = openfig('joint_mutual_info_components.fig', 'invisible');
fig2 = openfig('security_metrics.fig', 'invisible');

% 创建新的组合图形
new_fig = figure('Position', [100, 100, 1200, 600]);

% 复制第一个图形
subplot(1, 2, 1);
copyobj(allchild(get(fig1, 'CurrentAxes')), gca);

% 复制第二个图形
subplot(1, 2, 2);
copyobj(allchild(get(fig2, 'CurrentAxes')), gca);

% 关闭原始图形
close([fig1, fig2]);
```

## 🎨 美化和发表

### 1. 发表质量设置
```matlab
fig_handle = openfig('comprehensive_security_analysis.fig');

% 设置发表质量属性
set(fig_handle, 'PaperPositionMode', 'auto');
set(fig_handle, 'Color', 'white');

% 设置所有轴的属性
axes_handles = findall(fig_handle, 'Type', 'axes');
for i = 1:length(axes_handles)
    set(axes_handles(i), 'FontName', 'Times New Roman');
    set(axes_handles(i), 'FontSize', 12);
    set(axes_handles(i), 'LineWidth', 1.5);
end
```

### 2. 导出高质量图像
```matlab
% 导出为高分辨率PNG
print(fig_handle, 'publication_ready.png', '-dpng', '-r600');

% 导出为矢量格式
print(fig_handle, 'publication_ready.eps', '-depsc', '-tiff');
```

## 🔍 数据分析

### 从FIG文件中提取数值数据：
```matlab
function data = extract_fig_data(fig_filename)
    fig_handle = openfig(fig_filename, 'invisible');
    
    % 获取所有线条
    lines = findobj(fig_handle, 'Type', 'line');
    
    data = struct();
    for i = 1:length(lines)
        data.(['line_' num2str(i)]).x = get(lines(i), 'XData');
        data.(['line_' num2str(i)]).y = get(lines(i), 'YData');
        data.(['line_' num2str(i)]).name = get(lines(i), 'DisplayName');
    end
    
    close(fig_handle);
end

% 使用示例
data = extract_fig_data('joint_mutual_info_components.fig');
```

## 📊 交互式分析

### 1. 数据游标和缩放
```matlab
fig_handle = openfig('3d_mutual_info_landscape.fig');

% 启用数据游标
datacursormode on;

% 启用缩放
zoom on;

% 启用旋转（对3D图形）
rotate3d on;
```

### 2. 动态更新
```matlab
fig_handle = openfig('joint_mutual_info_components.fig');

% 获取线条句柄
lines = findobj(fig_handle, 'Type', 'line');

% 动态更新数据
new_y_data = get(lines(1), 'YData') * 1.1;  % 增加10%
set(lines(1), 'YData', new_y_data);

% 更新图例
legend('更新的联合MI', 'Sensing MI', 'Communication MI', 'Coupling');
```

## 💡 使用技巧

### 1. 快速预览所有图形
```matlab
% 创建图形缩略图
fig_files = dir('*.fig');
figure('Position', [100, 100, 1200, 800]);

for i = 1:min(6, length(fig_files))  % 最多显示6个
    subplot(2, 3, i);
    temp_fig = openfig(fig_files(i).name, 'invisible');
    copyobj(allchild(get(temp_fig, 'CurrentAxes')), gca);
    title(strrep(fig_files(i).name, '_', ' '), 'Interpreter', 'none');
    close(temp_fig);
end
```

### 2. 自动化报告生成
```matlab
% 生成包含所有图形的报告
fig_files = dir('*.fig');
report_fig = figure('Position', [100, 100, 1600, 1200]);

for i = 1:length(fig_files)
    subplot(3, 3, i);
    temp_fig = openfig(fig_files(i).name, 'invisible');
    copyobj(allchild(get(temp_fig, 'CurrentAxes')), gca);
    title(strrep(fig_files(i).name, '.fig', ''), 'Interpreter', 'none');
    close(temp_fig);
end

sgtitle('FD-ISAC Joint Mutual Information Analysis Report', 'FontSize', 16);
print(report_fig, 'complete_report.png', '-dpng', '-r300');
```

## 🎯 总结

FIG格式的优势：
- ✅ **完全交互式**：可以缩放、旋转、编辑
- ✅ **保留所有数据**：包含原始数值数据
- ✅ **MATLAB原生**：完美兼容MATLAB环境
- ✅ **可编辑性**：可以修改任何图形属性
- ✅ **高质量**：矢量格式，无损缩放

现在您可以在MATLAB中完全交互式地分析和编辑所有生成的图表！
